<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 进件审核管理系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
            font-weight: 400;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        /* 表格操作列 */
        .table-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            overflow-y: auto;
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 700px;
            max-width: 95%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 流程样式 */
        .process-flow {
            position: relative;
            padding-left: 40px;
        }

        .process-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border-color-light);
        }

        .flow-item {
            position: relative;
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
        }

        .flow-node {
            position: absolute;
            left: -28px;
            top: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--bg-container);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-node.completed {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.current {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .flow-node.pending {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-node.rejected {
            background: var(--error-red);
            border-color: var(--error-red);
        }

        .flow-content {
            flex: 1;
            min-width: 0;
        }

        .flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .flow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .flow-status.current {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .flow-status.pending {
            background: #f5f5f5;
            color: var(--text-secondary);
        }

        .flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .flow-institution {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .flow-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .flow-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-disabled);
        }

        .flow-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核中心</div>
                <div class="nav-item" onclick="switchToPage('rate')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    费率审核
                </div>
                <div class="nav-item" onclick="switchToPage('team')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    团队审核
                </div>
                <div class="nav-item" onclick="switchToPage('equipment')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>
                    设备审核
                </div>
                <div class="nav-item active" onclick="switchToPage('onboarding')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    进件审核
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核中心</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">进件审核</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <div class="page-container">
            <div class="page-header">
                <h1 class="page-title">进件审核</h1>
                <p class="page-description">管理进件黑名单审核申请</p>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">商户号</label>
                            <input type="text" placeholder="请输入商户号" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>未审核</option>
                                <option>审核通过</option>
                                <option>审核驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">申请类型</label>
                            <select class="form-select">
                                <option>全部类型</option>
                                <option>移出黑名单</option>
                                <option>加入白名单</option>
                                <option>风险等级调整</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">风险等级</label>
                            <select class="form-select">
                                <option>全部等级</option>
                                <option>低风险</option>
                                <option>中风险</option>
                                <option>高风险</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; align-items: end;">
                        <div class="form-group">
                            <label class="form-label">申请时间</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                                <span style="color: var(--text-secondary);">至</span>
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-default">重置</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                批量通过
                            </button>
                        </div>
                        
                        <!-- 审核配置区域 -->
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <!-- 配置按钮 -->
                            <button class="btn btn-default" onclick="openConfigModal()">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                                配置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>商户号</th>
                                <th>商户名</th>
                                <th>申请人</th>
                                <th>申请类型</th>
                                <th>当前状态</th>
                                <th>申请原因</th>
                                <th>申请时间</th>
                                <th>区县审核状态</th>
                                <th>市级审核状态</th>
                                <th>省级审核状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201001</td>
                                <td>潮州市邮政便民服务点</td>
                                <td>张三</td>
                                <td><span style="background: #e6f4ff; color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">移出黑名单</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">黑名单</span></td>
                                <td>已整改完成，申请移出黑名单</td>
                                <td>2024-12-15 09:30</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('ON001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201002</td>
                                <td>汕头市邮政营业厅</td>
                                <td>王五</td>
                                <td><span style="background: #f0f9ff; color: #0ea5e9; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">加入白名单</span></td>
                                <td><span style="background: #fffbeb; color: var(--warning-orange); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">观察期</span></td>
                                <td>优质商户，申请加入白名单</td>
                                <td>2024-12-14 14:20</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('ON002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201003</td>
                                <td>梅州市邮政代办点</td>
                                <td>赵六</td>
                                <td><span style="background: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">风险等级调整</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">高风险</span></td>
                                <td>申请降低风险等级至中风险</td>
                                <td>2024-12-13 16:45</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核驳回</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('ON003')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201004</td>
                                <td>揭阳市邮政支局</td>
                                <td>孙八</td>
                                <td><span style="background: #e6f4ff; color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">移出黑名单</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">黑名单</span></td>
                                <td>误判申请，请求移出黑名单</td>
                                <td>2024-12-12 11:30</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('ON004')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201005</td>
                                <td>清远市邮政便民点</td>
                                <td>周九</td>
                                <td><span style="background: #f0f9ff; color: #0ea5e9; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">加入白名单</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">正常</span></td>
                                <td>长期合作优质商户</td>
                                <td>2024-12-11 08:15</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('ON005')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 78 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">20条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">2</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 审核流程说明 -->
            <div class="card">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <svg style="width: 16px; height: 16px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        <h3 class="card-title" style="color: var(--primary-blue);">进件审核流程说明</h3>
                    </div>
                </div>
                <div class="card-body" style="background: #f8faff;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--primary-blue);">
                            <h4 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">黑名单管理</h4>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;"><span style="color: var(--primary-blue); font-weight: 500;">移出条件：</span>商户已完成整改，风险消除</div>
                                <div style="margin-bottom: 8px;"><span style="color: var(--primary-blue); font-weight: 500;">审核标准：</span>整改措施有效性评估</div>
                                <div><span style="color: var(--primary-blue); font-weight: 500;">处理时效：</span>7个工作日内完成审核</div>
                            </div>
                        </div>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--success-green);">
                            <h4 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">白名单管理</h4>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;"><span style="color: var(--success-green); font-weight: 500;">准入条件：</span>优质商户，信用记录良好</div>
                                <div style="margin-bottom: 8px;"><span style="color: var(--success-green); font-weight: 500;">审核标准：</span>交易数据和经营状况评估</div>
                                <div><span style="color: var(--success-green); font-weight: 500;">优惠政策：</span>享受费率优惠和绿色通道</div>
                            </div>
                        </div>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--warning-orange);">
                            <h4 style="margin: 0 0 12px 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">风险等级调整</h4>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;"><span style="color: var(--warning-orange); font-weight: 500;">评估维度：</span>交易异常、投诉记录、合规情况</div>
                                <div style="margin-bottom: 8px;"><span style="color: var(--warning-orange); font-weight: 500;">调整原则：</span>基于风险评估模型动态调整</div>
                                <div><span style="color: var(--warning-orange); font-weight: 500;">监控机制：</span>持续监控，定期复评</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看流程弹窗 -->
    <div id="process-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">查看流程</h3>
                <button class="modal-close" onclick="closeProcessModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="process-flow" id="process-timeline"></div>
            </div>
        </div>
    </div>

    <!-- 审核规则配置弹窗 -->
    <div id="config-modal" class="modal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">进件审核规则配置</h3>
                <button class="modal-close" onclick="closeConfigModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 24px;">
                    
                    


                    <!-- 进件审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">进件审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置进件申请的审核流程，可选择需要审核的机构：
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-province" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>省级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-city" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>市级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-county" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>区县机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--primary-blue); border-radius: 6px; cursor: pointer; font-size: 14px; background: var(--primary-blue-light);" class="checkbox-label">
                                <input type="checkbox" id="audit-outlet" checked style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>网点机构审核</span>
                            </label>
                        </div>
                        
                    </div>
                </div>

                <!-- 配置说明 -->
                <div style="padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid var(--primary-blue);">
                    <div style="font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">配置说明：</div>
                    <ul style="margin: 0; padding-left: 16px; color: var(--text-secondary); font-size: 13px; line-height: 1.6;">
                        <li>审核流程将按照勾选的机构层级从下往上逐级审核</li>
                        <li>规则修改后将实时生效，审核中的流程不受影响，后续新的申请将按照新的规则进行审核</li>
                        
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <script>
        // 页面切换函数
        function switchToPage(page) {
            const pages = {
                'rate': '账户补贴审核管理系统.html',
                'team': '客户经理审核管理系统.html', 
                'equipment': '设备审核管理系统.html',
                'onboarding': '进件审核管理系统.html'
            };
            
            if (pages[page]) {
                window.location.href = pages[page];
            }
        }

        // 进件审核流程数据
        const onboardingProcessData = {
            'ON001': {
                orderId: 'ON001',
                merchantName: '潮州市邮政便民服务点',
                applicant: '张三',
                applicationType: '移出黑名单',
                currentStatus: '黑名单',
                reason: '已整改完成，申请移出黑名单',
                submitTime: '2024-12-15 09:30',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '潮州市湘桥区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户已完成整改，风险控制措施到位，同意移出黑名单。',
                        time: '2024-12-15 10:15',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '潮州市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户整改效果良好，风险等级已降低，审核通过。',
                        time: '2024-12-15 14:30',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过，商户移出黑名单，恢复正常状态。',
                        time: '2024-12-15 16:45',
                        operator: '省级审核员'
                    }
                ]
            },
            'ON002': {
                orderId: 'ON002',
                merchantName: '汕头市便民超市',
                applicant: '李四',
                applicationType: '加入白名单',
                currentStatus: '正常',
                reason: '商户交易稳定，信用良好，申请加入白名单',
                submitTime: '2024-12-14 14:20',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '汕头市龙湖区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户经营状况良好，交易数据稳定，符合白名单标准。',
                        time: '2024-12-14 16:30',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汕头市邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '正在进行市级审核...',
                        time: '',
                        operator: ''
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '待审核',
                        content: '等待省级审核...',
                        time: '',
                        operator: ''
                    }
                ]
            },
            'ON003': {
                orderId: 'ON003',
                merchantName: '揭阳市小食店',
                applicant: '王五',
                applicationType: '风险等级调整',
                currentStatus: '高风险',
                reason: '已整改风险问题，申请调整为中风险',
                submitTime: '2024-12-13 11:45',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '揭阳市榕城区邮政分局',
                        status: 'rejected',
                        statusText: '审核驳回',
                        content: '意见：整改措施不够完善，风险隐患仍然存在，建议继续整改后重新申请。',
                        time: '2024-12-13 15:20',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '揭阳市邮政分公司',
                        status: 'pending',
                        statusText: '待审核',
                        content: '等待市级审核...',
                        time: '',
                        operator: ''
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '待审核',
                        content: '等待省级审核...',
                        time: '',
                        operator: ''
                    }
                ]
            },
            'ON004': {
                orderId: 'ON004',
                merchantName: '梅州市水果店',
                applicant: '赵六',
                applicationType: '移出黑名单',
                currentStatus: '黑名单',
                reason: '风险问题已解决，申请移出黑名单',
                submitTime: '2024-12-12 09:15',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '梅州市梅江区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户已完成整改，风险等级降低，同意移出黑名单。',
                        time: '2024-12-12 14:00',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '梅州市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：审核材料完整，整改效果明显，同意通过。',
                        time: '2024-12-12 17:30',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '正在进行省级最终审核...',
                        time: '',
                        operator: ''
                    }
                ]
            },
            'ON005': {
                orderId: 'ON005',
                merchantName: '清远市服装店',
                applicant: '孙七',
                applicationType: '加入白名单',
                currentStatus: '观察期',
                reason: '观察期表现良好，申请加入白名单',
                submitTime: '2024-12-11 16:40',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '清远市清城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户在观察期内表现良好，无异常交易，符合白名单条件。',
                        time: '2024-12-12 10:20',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '清远市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户信用记录良好，交易稳定，同意加入白名单。',
                        time: '2024-12-12 15:45',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过，商户成功加入白名单，享受相应优惠政策。',
                        time: '2024-12-13 09:30',
                        operator: '省级审核员'
                    }
                ]
            }
        };

        // 显示流程查看弹窗
        function showProcessModal(onboardingId) {
            const modal = document.getElementById('process-modal');
            const data = onboardingProcessData[onboardingId];
            
            if (!data) {
                alert('未找到该进件申请的流程信息');
                return;
            }
            
            generateOnboardingProcessFlow(data);
            modal.style.display = 'block';
        }

        function openConfigModal() {
            document.getElementById('config-modal').style.display = 'block';
        }

        function closeConfigModal() {
            document.getElementById('config-modal').style.display = 'none';
        }

        function saveConfig() {
            // 获取选中的审核级别
            const selectedLevels = [];
            document.querySelectorAll('#config-modal input[type="checkbox"]:checked').forEach(checkbox => {
                const label = checkbox.closest('label').querySelector('span').textContent;
                selectedLevels.push(label);
            });
            
            // 显示成功提示
            alert('进件审核规则配置已保存！');
            
            // 关闭弹窗
            closeConfigModal();
        }

        // 关闭流程查看弹窗
        function closeProcessModal() {
            document.getElementById('process-modal').style.display = 'none';
        }

        // 生成进件审核流程HTML
        function generateOnboardingProcessFlow(data) {
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node completed">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <span>发起申请</span>
                        <span class="flow-status completed">已提交</span>
                    </div>
                    <div class="flow-institution">${data.merchantName}</div>
                    <div class="flow-description">申请类型：${data.applicationType}，申请原因：${data.reason}</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${data.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${data.applicant}
                        </div>
                    </div>
                </div>
            `;
            timeline.appendChild(startItem);
            
            // 添加审核节点
            data.steps.forEach((step) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeIcon = '';
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${step.status}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <span>${step.level}</span>
                            <span class="flow-status ${step.status}">${step.statusText}</span>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                timeline.appendChild(flowItem);
            });
            
            // 添加结束节点
            const allCompleted = data.steps.every(step => step.status === 'completed');
            const hasRejected = data.steps.some(step => step.status === 'rejected');
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            let endTime = '';
            
            if (allCompleted) {
                endStatus = 'completed';
                endText = '审核完成';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                // 获取最后一个完成步骤的时间作为结束时间
                const lastCompletedStep = data.steps.filter(step => step.status === 'completed').pop();
                if (lastCompletedStep && lastCompletedStep.time) {
                    endTime = lastCompletedStep.time;
                }
            } else if (hasRejected) {
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                // 获取被拒绝步骤的时间作为终止时间
                const rejectedStep = data.steps.find(step => step.status === 'rejected');
                if (rejectedStep && rejectedStep.time) {
                    endTime = rejectedStep.time;
                }
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'pending'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${endText}</div>
                    ${endTime ? `
                        <div class="flow-meta">
                            <div class="flow-meta-item">
                                <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <polyline points="12,6 12,12 16,14"/>
                                </svg>
                                ${endTime} 完成
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
            timeline.appendChild(endItem);
        }

        document.addEventListener('DOMContentLoaded', () => {
            // 强制设置当前页面的菜单为激活状态
            const currentPage = 'onboarding'; 
            const navItems = document.querySelectorAll('.nav-section .nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('onclick') === `switchToPage('${currentPage}')`) {
                    item.classList.add('active');
                }
            });
            
            const ruleOptions = document.querySelectorAll('.rule-option');
            ruleOptions.forEach(option => {
                option.addEventListener('click', (event) => {
                    const checkbox = option.querySelector('input[type="checkbox"]');
                    if (event.target.type !== 'checkbox') {
                        checkbox.checked = !checkbox.checked;
                    }
                    if (checkbox.checked) {
                        option.classList.add('active');
                    } else {
                        option.classList.remove('active');
                    }
                });
            });
        });
    </script>
</body>
</html> 