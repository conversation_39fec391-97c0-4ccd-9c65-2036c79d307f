<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进件提交申请页 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .form-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-input {
            background: transparent;
            border: none;
            outline: none;
            width: 100%;
            font-size: 16px;
            color: #333;
        }
        
        .form-input::placeholder {
            color: #999;
        }
        
        .form-label {
            color: #333;
            font-weight: 500;
            min-width: 100px;
        }
        
        .section-title {
            color: #333;
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 16px;
        }
        
        .submit-button {
            background: var(--postal-green);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 16px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .submit-button:hover {
            background: var(--postal-green-dark);
        }
        
        .submit-button:active {
            transform: scale(0.98);
        }
        
        .success-modal {
            animation: modalSlideUp 0.3s ease-out;
        }
        
        @keyframes modalSlideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 时间输入字段样式 */
        input[type="datetime-local"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            background: transparent;
            bottom: 0;
            color: transparent;
            cursor: pointer;
            height: auto;
            left: 0;
            position: absolute;
            right: 0;
            top: 0;
            width: auto;
        }

        .time-input-wrapper {
            position: relative;
        }

        .time-input-wrapper::after {
            content: '\f073';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">进件提交申请页</h1>
            <div class="w-10"></div>
        </div>
    </div>

    <!-- 表单内容 -->
    <div class="px-4 py-6 space-y-6">
        <!-- 商户信息部分 -->
        <div class="form-section p-4">
            <h2 class="section-title">商户信息</h2>
            
           
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">商户负责人</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="自动带入"
                    id="merchantOwner"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">负责人手机号</label>
                <input 
                    type="tel" 
                    class="form-input text-right" 
                    placeholder="自动带入"
                    id="ownerPhone"
                    pattern="[0-9]{11}"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">负责人身份证号</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="自动带入"
                    id="ownerIdCard"
                    pattern="[0-9X]{18}"
                    required
                >
            </div>
            <div class="form-item py-4 flex items-center">
                <label class="form-label">银行卡号</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="自动带入"
                    id="merchantName"
                    required
                >
            </div>
            <div class="form-item py-4 flex items-center">
                <label class="form-label">营业执照号</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="自动带入"
                    id="businessLicense"
                    required
                >
            </div>
            
            
            
        </div>

        <!-- 申请信息部分 -->
        <div class="form-section p-4">
            <h2 class="section-title">申请信息</h2>

            <!-- 申请进件时间 -->
            <div class="form-item py-4">
                <label class="form-label block mb-2">申请进件时间</label>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm text-gray-600 min-w-16">开始时间</label>
                        <div class="time-input-wrapper flex-1">
                            <input
                                type="datetime-local"
                                class="form-input border border-gray-300 rounded-lg px-3 py-2 text-sm w-full"
                                id="applyStartTime"
                                required
                            >
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm text-gray-600 min-w-16">结束时间</label>
                        <div class="time-input-wrapper flex-1">
                            <input
                                type="datetime-local"
                                class="form-input border border-gray-300 rounded-lg px-3 py-2 text-sm w-full bg-gray-100"
                                id="applyEndTime"
                                readonly
                                required
                            >
                        </div>
                    </div>
                </div>

                <!-- 说明框 -->
                <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-info-circle text-blue-500 text-sm mt-0.5"></i>
                        <p class="text-sm text-blue-700">
                            审批通过后，请尽快在时间范围内进件，否则超时需重新申请。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 申请原因 -->
            <div class="form-item py-4">
                <label class="form-label block mb-2">申请原因</label>
                <textarea
                    class="form-input resize-none border border-gray-300 rounded-lg px-3 py-2"
                    placeholder="请详细说明进件申请的原因和必要性"
                    id="applyReason"
                    rows="4"
                    required
                ></textarea>
            </div>
        </div>
    </div>

    <!-- 底部提交按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <button class="submit-button" onclick="submitApplication()">
            <i class="fas fa-paper-plane mr-2"></i>
            提交申请
        </button>
    </div>

    <!-- 成功提示弹窗 -->
    <div id="successModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" onclick="closeSuccessModal()"></div>
        <div class="absolute bottom-0 left-0 right-0">
            <div class="success-modal bg-white rounded-t-2xl p-6">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">申请提交成功</h3>
                    <p class="text-gray-600 mb-6">您的进件申请已提交，我们将在3-5个工作日内完成审核。</p>
                    <div class="flex space-x-3">
                        <button 
                            onclick="goToDeviceManagement()" 
                            class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium"
                        >
                            返回首页
                        </button>
                        <button 
                            onclick="continueApply()" 
                            class="flex-1 py-3 px-4 postal-green text-white rounded-lg font-medium"
                        >
                            继续申请
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 返回按钮
        function goBack() {
            history.back();
        }

        // 提交申请
        function submitApplication() {
            // 获取表单数据
            const formData = {
                
                merchantOwner: document.getElementById('merchantOwner').value,
                ownerPhone: document.getElementById('ownerPhone').value,
                ownerIdCard: document.getElementById('ownerIdCard').value,
                businessLicense: document.getElementById('businessLicense').value,
               
                applyStartTime: document.getElementById('applyStartTime').value,
                applyEndTime: document.getElementById('applyEndTime').value,
                applyReason: document.getElementById('applyReason').value
            };

            // 验证必填字段
            const requiredFields = [
               
                { field: 'merchantOwner', name: '商户负责人' },
                { field: 'ownerPhone', name: '负责人手机号' },
                { field: 'ownerIdCard', name: '负责人身份证号' },
                { field: 'businessLicense', name: '营业执照号' },
                
                { field: 'applyStartTime', name: '申请开始时间' },
                { field: 'applyEndTime', name: '申请结束时间' },
                { field: 'applyReason', name: '申请原因' }
            ];

            for (let item of requiredFields) {
                if (!formData[item.field] || formData[item.field].trim() === '') {
                    alert(`请填写${item.name}`);
                    document.getElementById(item.field).focus();
                    return;
                }
            }

            // 验证手机号格式
            const phonePattern = /^1[3-9]\d{9}$/;
            if (!phonePattern.test(formData.ownerPhone)) {
                alert('请输入正确的手机号码');
                document.getElementById('ownerPhone').focus();
                return;
            }

            // 验证身份证号格式
            const idCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardPattern.test(formData.ownerIdCard)) {
                alert('请输入正确的身份证号码');
                document.getElementById('ownerIdCard').focus();
                return;
            }

            // 验证营业执照号格式（统一社会信用代码）
            const licensePattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            if (!licensePattern.test(formData.businessLicense)) {
                alert('请输入正确的营业执照号（统一社会信用代码）');
                document.getElementById('businessLicense').focus();
                return;
            }

            // 验证开始时间
            const startTime = new Date(formData.applyStartTime);
            const currentTime = new Date();

            if (startTime < currentTime) {
                alert('申请开始时间不能早于当前时间');
                document.getElementById('applyStartTime').focus();
                return;
            }

            // 验证申请原因长度
            if (formData.applyReason.length < 10) {
                alert('申请原因不能少于10个字符');
                document.getElementById('applyReason').focus();
                return;
            }

            // 模拟提交
            console.log('提交进件申请数据:', formData);
            
            // 显示成功弹窗
            showSuccessModal();
        }

        // 显示成功弹窗
        function showSuccessModal() {
            document.getElementById('successModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭成功弹窗
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 返回设备管理页面
        function goToDeviceManagement() {
            window.location.href = '移动端设备管理.html';
        }

        // 继续申请
        function continueApply() {
            closeSuccessModal();
            // 清空表单
            document.querySelectorAll('input, select, textarea').forEach(element => {
                element.value = '';
            });
            // 滚动到顶部
            window.scrollTo(0, 0);
        }

        // 防止弹窗内容区域点击时关闭弹窗
        document.querySelector('.success-modal').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 页面加载时添加底部间距，避免被固定按钮遮挡
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.paddingBottom = '100px';
        });

        // 手机号输入格式化
        document.getElementById('ownerPhone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 身份证号输入格式化
        document.getElementById('ownerIdCard').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9X]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value.toUpperCase();
        });

        // 营业执照号输入格式化
        document.getElementById('businessLicense').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9A-HJ-NPQRTUWXY]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value.toUpperCase();
        });

        // 初始化时间输入字段
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认开始时间为当前时间
            const now = new Date();
            const currentDateTime = now.toISOString().slice(0, 16);
            document.getElementById('applyStartTime').value = currentDateTime;

            // 设置默认结束时间为一个月后
            const oneMonthLater = new Date(now);
            oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
            const oneMonthLaterDateTime = oneMonthLater.toISOString().slice(0, 16);
            document.getElementById('applyEndTime').value = oneMonthLaterDateTime;

            // 设置最小时间为当前时间
            document.getElementById('applyStartTime').min = currentDateTime;
        });

        // 开始时间变化时，自动设置结束时间为一个月后
        document.getElementById('applyStartTime').addEventListener('change', function(e) {
            const startTime = e.target.value;
            if (startTime) {
                const startDate = new Date(startTime);
                const endDate = new Date(startDate);
                endDate.setMonth(endDate.getMonth() + 1);

                // 设置结束时间为一个月后
                document.getElementById('applyEndTime').value = endDate.toISOString().slice(0, 16);
            }
        });
    </script>
</body>
</html> 