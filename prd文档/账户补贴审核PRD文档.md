# 账户补贴审核管理系统 PRD

## 1. 项目背景

### 1.1 业务背景
账户补贴审核是微邮付系统中的核心业务流程，涉及区县、地市、省级三级机构的审核权限管理。随着业务量的增长和效率提升需求，现有的人工审核模式已无法满足快速响应的业务要求，急需引入智能化的免审机制。

### 1.2 问题描述
- **审核效率低**：小额补贴申请仍需人工审核，占用大量审核资源
- **审核层级复杂**：三级机构权限划分不够清晰，存在重复审核情况
- **风险控制不足**：缺乏基于金额阈值的自动风控机制
- **用户体验差**：小额申请审核周期长，影响商户体验

### 1.3 解决方案
通过引入条件化免审机制，在保证风险可控的前提下，大幅提升小额补贴审核效率。

## 2. 产品目标

### 2.1 核心目标
- **效率提升**：小额补贴申请实现秒级自动审核通过
- **风险可控**：基于累计金额阈值的多层级风控体系
- **权限清晰**：三级机构审核权限明确划分，避免重复审核
- **体验优化**：商户小额申请即时生效，提升满意度

### 2.2 量化指标
- 小额补贴审核效率提升 90% 以上
- 审核人员工作量减少 60% 以上
- 商户满意度提升至 95% 以上
- 风险事件发生率控制在 0.1% 以内

## 3. 功能需求

### 3.1 免审配置功能

#### 3.1.1 开关控制
- **功能描述**：支持三级机构独立配置免审开关
- **配置权限**：
  - 省级机构：可配置省级免审规则
  - 地市机构：可配置地市级免审规则
  - 区县机构：可配置区县级免审规则
- **状态管理**：支持启用/停用状态切换

#### 3.1.2 免审金额配置
- **功能描述**：支持设置同一商户累计免审金额阈值
- **配置规则**：
  - 省级机构：可设置较高的免审金额（如1000元）
  - 地市机构：可设置中等的免审金额（如300元）
  - 区县机构：可设置较低的免审金额（如400元）
- **金额限制**：
  - 最小金额：1元
  - 最大金额：10000元
  - 支持小数点后2位

#### 3.1.3 配置管理
- **查看配置**：支持查看当前免审配置详情
- **编辑配置**：支持修改免审金额和开关状态
- **配置历史**：记录配置变更历史和操作人

### 3.2 审核流程优化

#### 3.2.1 智能路由机制
- **优先级规则**：按省级 → 地市 → 区县的顺序进行规则匹配
- **路由逻辑**：
  1. 首先检查省级免审规则
  2. 其次检查地市级免审规则
  3. 最后检查区县级免审规则
  4. 选择第一个不满足免审条件的层级进行审核

#### 3.2.2 累计金额计算
- **计算周期**：按自然月计算商户累计补贴金额
- **计算范围**：包含已通过和待审核的补贴申请
- **实时更新**：申请提交时实时计算累计金额

#### 3.2.3 审核权限管理
- **审核权限**：只有不满足免审条件的最高层级机构有审核权
- **查看权限**：下级机构可查看但无法审核
- **驳回权限**：下级机构在申请未审核前可以驳回

## 4. 业务流程

### 4.1 免审配置流程

```
管理员登录 → 进入免审配置 → 开启免审开关 → 设置免审金额 → 保存配置 → 配置生效
```

**详细步骤：**
1. 机构管理员登录系统
2. 进入"政策审核"页面
3. 点击"免审配置"按钮
4. 开启免审开关
5. 设置累计免审金额阈值
6. 保存配置，系统验证金额范围
7. 配置立即生效，影响后续申请

### 4.2 申请审核流程

```
商户提交申请 → 系统计算累计金额 → 匹配免审规则 → 确定审核层级 → 执行审核流程
```

**详细步骤：**
1. 商户提交账户补贴申请
2. 系统计算该商户当月累计申请金额
3. 按优先级匹配各层级免审规则
4. 确定最终审核层级或免审处理
5. 执行相应的审核流程

### 4.3 免审判断逻辑

#### 4.3.1 规则匹配示例
**配置场景：**
- 省级：免审金额 1000元，开关开启
- 地市：免审金额 300元，开关开启  
- 区县：免审金额 400元，开关开启

**申请案例：商户申请 350元补贴**

**判断流程：**
1. **省级检查**：350 < 1000，满足省级免审条件
2. **地市检查**：350 > 300，不满足地市免审条件
3. **区县检查**：350 < 400，满足区县免审条件
4. **最终结果**：地市级审核（第一个不满足免审条件的层级）

#### 4.3.2 特殊情况处理
- **所有层级都满足免审**：系统自动通过
- **所有层级都不满足免审**：区县级审核
- **免审开关关闭**：按原有审核流程处理
- **配置冲突**：以上级机构配置为准

## 5. 界面设计

### 5.1 免审配置界面

#### 5.1.1 配置入口
- **位置**：政策审核页面，批量通过按钮右侧
- **样式**：开关组件 + 配置按钮
- **状态显示**：
  - 未配置：显示"免审配置"按钮
  - 已配置：显示开关状态和当前金额

#### 5.1.2 配置弹窗
**弹窗标题**：账户补贴免审配置

**配置项：**
1. **免审开关**
   - 组件类型：Toggle Switch
   - 默认状态：关闭
   - 说明文字：开启后，低于设定金额的申请将自动通过

2. **免审金额**
   - 组件类型：数字输入框
   - 单位：元
   - 范围：1-10000
   - 占位符：请输入免审金额阈值

3. **当前累计说明**
   - 显示内容：当月累计计算规则说明
   - 样式：信息提示框

**操作按钮：**
- 保存：保存配置并关闭弹窗
- 取消：不保存直接关闭弹窗

### 5.2 状态展示界面

#### 5.2.1 配置状态显示
- **已开启状态**：
  - 显示：绿色开关 + "免审已开启(≤300元)"
  - 操作：点击可编辑配置

- **已关闭状态**：
  - 显示：灰色开关 + "免审已关闭"
  - 操作：点击可重新开启

#### 5.2.2 审核记录标识
- **免审通过**：在状态列显示"自动通过"标签
- **层级审核**：显示具体审核机构名称
- **驳回处理**：显示驳回机构和原因

## 6. 技术实现

### 6.1 数据结构设计

#### 6.1.1 免审配置表
```sql
CREATE TABLE auto_approve_config (
    id BIGINT PRIMARY KEY,
    institution_id BIGINT NOT NULL,
    institution_level VARCHAR(20) NOT NULL, -- 'PROVINCE', 'CITY', 'COUNTY'
    approve_type VARCHAR(50) NOT NULL, -- 'ACCOUNT_SUBSIDY'
    is_enabled BOOLEAN DEFAULT FALSE,
    amount_threshold DECIMAL(10,2),
    created_by BIGINT,
    created_at TIMESTAMP,
    updated_by BIGINT,
    updated_at TIMESTAMP
);
```

#### 6.1.2 审核记录扩展
```sql
ALTER TABLE audit_record ADD COLUMN (
    auto_approve_flag BOOLEAN DEFAULT FALSE,
    approve_institution_level VARCHAR(20),
    monthly_accumulated_amount DECIMAL(10,2)
);
```

### 6.2 核心算法

#### 6.2.1 累计金额计算
```javascript
function calculateMonthlyAmount(merchantId, currentMonth) {
    // 查询当月该商户所有申请金额（已通过 + 待审核）
    const approvedAmount = getApprovedAmount(merchantId, currentMonth);
    const pendingAmount = getPendingAmount(merchantId, currentMonth);
    return approvedAmount + pendingAmount;
}
```

#### 6.2.2 免审规则匹配
```javascript
function matchAutoApproveRule(merchantId, requestAmount) {
    const monthlyAmount = calculateMonthlyAmount(merchantId);
    const totalAmount = monthlyAmount + requestAmount;
    
    const rules = getAutoApproveRules(); // 按优先级排序
    
    for (let rule of rules) {
        if (rule.isEnabled && totalAmount > rule.amountThreshold) {
            return {
                needApproval: true,
                approvalLevel: rule.institutionLevel,
                threshold: rule.amountThreshold
            };
        }
    }
    
    return {
        needApproval: false,
        autoApprove: true
    };
}
```

## 7. 风险控制

### 7.1 业务风险
- **金额风险**：设置最大免审金额上限
- **频次风险**：监控异常申请频次
- **商户风险**：建立商户信用评级机制

### 7.2 技术风险
- **配置冲突**：严格的配置验证机制
- **计算错误**：累计金额计算的准确性保证
- **并发问题**：分布式锁保证数据一致性

### 7.3 监控告警
- **异常申请监控**：单商户短时间大量申请
- **免审比例监控**：免审比例异常增长
- **金额监控**：超阈值申请的实时告警

## 8. 测试用例

### 8.1 配置功能测试
1. **开关控制测试**
   - 开启免审开关，验证状态保存
   - 关闭免审开关，验证状态保存
   - 重复开关切换，验证状态稳定性

2. **金额配置测试**
   - 设置合法金额范围，验证保存成功
   - 设置超出范围金额，验证错误提示
   - 设置小数金额，验证精度处理

### 8.2 审核流程测试
1. **免审判断测试**
   - 低于阈值申请，验证自动通过
   - 高于阈值申请，验证转入审核
   - 临界值申请，验证判断准确性

2. **多层级规则测试**
   - 多层级配置冲突，验证优先级处理
   - 部分层级关闭，验证规则跳过
   - 全部层级满足，验证自动通过

### 8.3 权限控制测试
1. **审核权限测试**
   - 验证只有指定层级可审核
   - 验证下级机构只能查看
   - 验证驳回权限正确分配

2. **配置权限测试**
   - 验证各层级只能配置自己的规则
   - 验证配置修改权限控制
   - 验证配置查看权限控制

## 9. 上线计划

### 9.1 分阶段上线
**第一阶段（1周）**：基础配置功能
- 免审开关和金额配置
- 基础界面和交互

**第二阶段（2周）**：核心审核逻辑
- 免审判断算法
- 多层级规则匹配
- 审核流程优化

**第三阶段（1周）**：监控和优化
- 监控告警机制
- 性能优化
- 用户培训

### 9.2 回滚预案
- **数据回滚**：配置表数据备份和恢复
- **功能回滚**：免审功能开关，可快速关闭
- **流程回滚**：恢复原有审核流程

## 10. 效果评估

### 10.1 评估指标
- **效率指标**：平均审核时长、自动通过率
- **质量指标**：审核准确率、风险事件率
- **体验指标**：用户满意度、投诉率

### 10.2 监控机制
- **实时监控**：关键指标实时大盘
- **定期报告**：周报、月报数据分析
- **异常告警**：指标异常时及时告警

---

**文档版本**：V1.0  
**创建时间**：2024-12-15  
**创建人**：产品经理  
**审核人**：技术负责人、业务负责人 