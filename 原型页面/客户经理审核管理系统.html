<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 客户经理审核管理系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-blue-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 14px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-primary:active {
            background: var(--primary-blue-active);
            border-color: var(--primary-blue-active);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-danger {
            background: var(--error-red);
            border-color: var(--error-red);
            color: #ffffff;
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
            line-height: 1.5715;
        }

        .data-table th input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
            font-weight: 400;
            line-height: 1.5715;
        }

        .data-table td,
        .data-table td span,
        .data-table td button,
        .data-table td input {
            font-size: 14px !important;
            font-weight: 400 !important;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif !important;
        }

        .data-table td .btn-link {
            font-weight: 400 !important;
        }

        .data-table td input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 表格操作列 */
        .table-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-actions .btn-link {
            font-size: 14px;
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-approved {
            background: #dcfce7;
            color: var(--success-green);
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-normal {
            background: #dcfce7;
            color: var(--success-green);
        }

        .status-reviewing {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        /* 头像样式 */
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            overflow-y: auto;
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 520px;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 复选框标签样式 */
        .checkbox-label {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .checkbox-label:hover {
            border-color: var(--primary-blue);
            background: var(--primary-blue-light);
        }

        .checkbox-label input[type="checkbox"]:checked + span {
            color: var(--primary-blue);
            font-weight: 500;
        }

        /* 流程查看弹窗专用样式 */
        .process-modal-content {
            width: 700px;
            max-width: 95vw;
            max-height: 90vh;
            height: auto;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            margin: 5vh auto;
            overflow: hidden;
        }

        .process-modal-header {
            flex-shrink: 0;
            border-bottom: 1px solid var(--border-color-light);
        }

        .process-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            max-height: calc(90vh - 120px);
            min-height: 0;
        }

        /* 美化滚动条 */
        .process-modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .process-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 流程时间线 */
        .process-flow {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
        }

        .process-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 20px;
            width: 2px;
            background: var(--border-color-light);
        }

        .flow-item {
            position: relative;
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
        }

        .flow-item:last-child {
            margin-bottom: 0;
        }

        .flow-node {
            position: absolute;
            left: -28px;
            top: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--bg-container);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-node.start {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.completed {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.current {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .flow-node.pending {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-node.rejected {
            background: var(--error-red);
            border-color: var(--error-red);
        }

        .flow-node.end {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-content {
            flex: 1;
            min-width: 0;
        }

        .flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .flow-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .flow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .flow-status.current {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .flow-status.pending {
            background: #f5f5f5;
            color: var(--text-secondary);
        }

        .flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .flow-institution {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .flow-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
            word-wrap: break-word;
            word-break: break-all;
        }

        .flow-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-disabled);
            flex-wrap: wrap;
        }

        .flow-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        /* 多流程标签页样式 */
        .flow-tabs {
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .flow-tab-header {
            display: flex;
            padding: 0 24px;
        }

        .flow-tab-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 400;
            transition: all 0.2s ease;
            position: relative;
        }

        .flow-tab-item:hover {
            color: var(--primary-blue);
        }

        .flow-tab-item.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
            font-weight: 500;
        }

        .flow-tab-item span {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 历史流程选择器 */
        .history-flow-selector {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            background: #fafafa;
        }

        .history-flow-selector select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-container);
        }

        .history-flow-selector select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        /* 历史流程折叠组件样式 */
        .history-flow-item {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .history-flow-item:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .history-flow-header {
            padding: 16px 20px;
            background: #fafafa;
            border-bottom: 1px solid var(--border-color-light);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
            user-select: none;
        }

        .history-flow-header:hover {
            background: #f0f0f0;
        }

        .history-flow-header.expanded {
            background: var(--primary-blue-light);
            border-bottom-color: var(--primary-blue);
        }

        .history-flow-header-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .history-flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .history-flow-meta {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .history-flow-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .history-flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .history-flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .history-flow-status.reviewing {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .history-flow-toggle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--bg-container);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .history-flow-toggle svg {
            width: 12px;
            height: 12px;
            color: var(--text-secondary);
            transition: transform 0.3s ease;
        }

        .history-flow-header.expanded .history-flow-toggle {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .history-flow-header.expanded .history-flow-toggle svg {
            color: white;
            transform: rotate(180deg);
        }

        .history-flow-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .history-flow-content.expanded {
            max-height: 2000px;
        }

        .history-flow-timeline {
            padding: 20px;
            position: relative;
            padding-left: 60px;
        }

        .history-flow-timeline::before {
            content: '';
            position: absolute;
            left: 40px;
            top: 0;
            bottom: 20px;
            width: 2px;
            background: var(--border-color-light);
        }

        /* 开发说明卡片样式 */
        .dev-info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin: 24px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .dev-info-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .dev-info-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dev-info-title {
            display: flex;
            align-items: center;
            color: #ffffff;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .dev-info-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            opacity: 0.9;
        }

        .dev-info-body {
            padding: 24px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .dev-info-section {
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .dev-info-section:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .dev-info-section h4 {
            color: var(--primary-blue);
            font-size: 14px;
            font-weight: 600;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
        }

        .dev-info-section h4:before {
            content: '';
            width: 4px;
            height: 16px;
            background: var(--primary-blue);
            border-radius: 2px;
            margin-right: 8px;
        }

        .dev-info-section p {
            color: var(--text-primary);
            font-size: 13px;
            line-height: 1.6;
            margin: 0;
        }

        .dev-info-section strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .dev-info-list {
            margin: 0;
            padding-left: 16px;
            color: var(--text-primary);
            font-size: 13px;
            line-height: 1.6;
        }

        .dev-info-list li {
            margin-bottom: 6px;
            position: relative;
        }

        .dev-info-list li:before {
            content: '•';
            color: var(--primary-blue);
            font-weight: bold;
            position: absolute;
            left: -12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dev-info-body {
                grid-template-columns: 1fr;
                gap: 16px;
                padding: 20px;
            }
            
            .dev-info-section {
                padding: 16px;
            }
            
            .dev-info-header {
                padding: 14px 20px;
            }
            
            .dev-info-title {
                font-size: 15px;
            }
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            .process-modal-content {
                width: 95%;
                margin: 5vh auto;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar fixed left-0 top-0 h-full z-40">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核中心</div>
                <div class="nav-item" onclick="switchToPage('rate')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    费率审核
                </div>
                <div class="nav-item active" onclick="switchToPage('team')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    团队审核
                </div>
                <div class="nav-item" onclick="switchToPage('equipment')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>
                    设备审核
                </div>
                <div class="nav-item" onclick="switchToPage('onboarding')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    进件审核
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核配置</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                    审核规则配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核中心</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">团队审核</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 客户经理审核页面 -->
        <div class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">团队审核</h1>
                <p class="page-description">管理客户经理的添加申请和审核流程</p>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">姓名</label>
                            <input type="text" placeholder="请输入姓名" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="text" placeholder="请输入手机号" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>审核中</option>
                                <option>已通过</option>
                                <option>已驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">区（县）分公司</label>
                            <select class="form-select">
                                <option>全部分公司</option>
                                <option>黄陵县分公司</option>
                                <option>城固县分公司</option>
                                <option>德保县分公司</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; align-items: end;">
                        <div class="form-group">
                            <label class="form-label">添加时间</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                                <span style="color: var(--text-secondary);">至</span>
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-default">重置</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    
                        <button class="btn btn-default" onclick="openConfigModal()">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                            </svg>
                            配置
                        </button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>头像</th>
                                <th>姓名</th>
                                <th>手机号</th>
                                <th>状态</th>
                                <th>区（县）分公司</th>
                                <th>营业所</th>
                                
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>马小明</td>
                                <td>18221860290</td>
                                <td><span class="status-tag status-normal">正常</span></td>
                                <td>黄陵县分公司</td>
                                <td>黄陵县店头营业所</td>
                                <td>2025-06-20 15:49:55</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" style="color: var(--error-red);">删除</button>
                                        <button class="btn-link" onclick="showProcessModal('CM004')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>王莹</td>
                                <td>18740618843</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>黄陵县分公司</td>
                                <td>黄陵县店头营业所</td>
                                 <td>2025-06-20 15:48:31</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" onclick="handleAudit('王莹', '区县机构', true)">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>胡丽娜</td>
                                <td>17762243634</td>
                                <td><span class="status-tag status-normal">正常</span></td>
                                <td>城固县分公司</td>
                                <td>城固县天明营业所</td>
                                <td>2025-06-20 15:42:41</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" style="color: var(--error-red);">删除</button>
                                        <button class="btn-link" onclick="showProcessModal('CM005')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>胡丽娜1</td>
                                <td>17762243634</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>城固县分公司</td>
                                <td>城固县龙头营业所</td>
                                <td>2025-06-20 15:41:41</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" onclick="handleAudit('梁芳丽', '网点机构', true)">审核</button>
                                        
                                        <button class="btn-link" onclick="showProcessModal('CM002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>梁芳丽</td>
                                <td>18074840225</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>德保县分公司</td>
                                <td>德保县东关营业所</td>
                                <td>2025-06-20 15:27:08</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" onclick="handleAudit('梁芳丽', '网点机构', true)">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM003')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>李明华</td>
                                <td>13567892345</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>西安市分公司</td>
                                <td>西安市雁塔营业所</td>
                                <td>2025-06-20 14:15:22</td>
                                <td>
                                    <div class="table-actions">
                                         <button class="btn-link" onclick="handleAudit('李明华', '市级机构', true)">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM006')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>张小红</td>
                                <td>15698734521</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>宝鸡市分公司</td>
                                <td>宝鸡市金台营业所</td>
                                <td>2025-06-20 13:42:18</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" onclick="handleAudit('张小红', '区县机构', true)">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM007')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div class="avatar">
                                        <svg style="width: 16px; height: 16px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                            <circle cx="12" cy="7" r="4"/>
                                        </svg>
                                    </div>
                                </td>
                                <td>赵建国</td>
                                <td>18923456789</td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>咸阳市分公司</td>
                                <td>咸阳市秦都营业所</td>
                                <td>2025-06-21 09:15:30</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link" onclick="handleAudit('赵建国', '省级机构', true)">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM008')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 8814 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">10条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">2</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">3</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">4</button>
                            <span style="padding: 4px 8px; color: var(--text-secondary);">...</span>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">8814</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                            前往 
                            <input type="number" value="1" style="width: 60px; padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; text-align: center;" min="1"> 
                            页
                        </div>
                    </div>
                </div>
            </div>

            <!-- 开发说明提示框 -->
            <div class="dev-info-card">
                <div class="dev-info-header">
                    <div class="dev-info-title">
                        <svg class="dev-info-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        开发说明
                    </div>
                </div>
                <div class="dev-info-body">
                    <div class="dev-info-section">
                        <h4>配置功能</h4>
                        <p>支持由<strong>省级机构</strong>在用户业务员查询页面，进行配置审核规则，支持配置审核节点。</p>
                    </div>
                    
                    <div class="dev-info-section">
                        <h4>流程生效</h4>
                        <p>配置完成后，流程<strong>仅对后续新流程生效</strong>，历史数据不作改动。</p>
                    </div>
                    
                    <div class="dev-info-section">
                        <h4>流程查看</h4>
                        <p>每个客户经理申请提交后，可通过<strong>查看流程</strong>按钮查看整个审核进度。</p>
                    </div>
                    
                    <div class="dev-info-section">
                        <h4>数据展示逻辑</h4>
                        <ul class="dev-info-list">
                            <li>逐级审核时，当前审核机构及以下机构可见</li>
                            <li>驳回时，驳回机构及以下机构可见</li>
                        </ul>
                    </div>
                    
                    <div class="dev-info-section">
                        <h4>驳回处理方式一</h4>
                        <ul class="dev-info-list">
                            <li>驳回后新增审核节点</li>
                            <li>重新审核后直送驳回节点</li>
                            <li>后续按正常流程审核</li>
                        </ul>
                        <h4>驳回处理方式二</h4>
                        <ul class="dev-info-list">
                            <li>驳回后退回至发起人节点</li>
                            <li>发起人可选择重新申请或删除</li>
                            <li>后续按正常流程逐级审核</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
    </div>

    <!-- 审核弹窗 -->
    <div id="audit-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">客户经理审核</h3>
                <button class="modal-close" onclick="closeAuditModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">

                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">审核意见 <span style="color: var(--error-red);">*</span></label>
                    <textarea style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; resize: vertical;" placeholder="请输入审核意见"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeAuditModal()">取消</button>
                <button class="btn btn-danger" onclick="rejectAudit()">驳回</button>
                <button class="btn btn-success" onclick="approveAudit()">通过</button>
            </div>
        </div>
    </div>

    <!-- 查看流程弹窗 -->
    <div id="process-modal" class="modal">
        <div class="modal-content process-modal-content">
            <div class="modal-header process-modal-header">
                <h3 class="modal-title">查看流程</h3>
                <button class="modal-close" onclick="closeProcessModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <!-- 多流程标签页 -->
            <div id="flow-tabs" class="flow-tabs" style="display: none;">
                <div class="flow-tab-header">
                    <div class="flow-tab-item active" data-tab="current">
                        <span>当前流程</span>
                    </div>
                    <div class="flow-tab-item" data-tab="history">
                        <span>历史流程</span>
                    </div>
                </div>
            </div>
            <div class="modal-body process-modal-body">
                <!-- 审核流程时间线 -->
                <div class="process-flow" id="process-timeline">
                    <!-- 动态生成的流程内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 审核规则配置弹窗 -->
    <div id="config-modal" class="modal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">客户经理审核规则配置</h3>
                <button class="modal-close" onclick="closeConfigModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 24px;">
                    
                    


                    <!-- 客户经理审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">客户经理审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置客户经理申请的审核流程，可选择需要审核的机构：
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-province" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>省级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-city" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>市级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-county" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>区县机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--primary-blue); border-radius: 6px; cursor: pointer; font-size: 14px; background: var(--primary-blue-light);" class="checkbox-label">
                                <input type="checkbox" id="audit-outlet" checked style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>网点机构审核</span>
                            </label>
                        </div>
                        
                    </div>
                </div>

                <!-- 配置说明 -->
                <div style="padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid var(--primary-blue);">
                    <div style="font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">配置说明：</div>
                    <ul style="margin: 0; padding-left: 16px; color: var(--text-secondary); font-size: 13px; line-height: 1.6;">
                        <li>审核流程将按照勾选的机构层级从下往上逐级审核</li>
                        <li>规则修改后将实时生效，审核中的流程不受影响，后续新的申请将按照新的规则进行审核</li>
                        
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <script>
        // 页面切换函数
        function switchToPage(page) {
            const pages = {
                'rate': '账户补贴审核管理系统.html',
                'team': '客户经理审核管理系统.html',
                'equipment': '设备审核管理系统.html',
                'onboarding': '进件审核管理系统.html'
            };
            
            if (pages[page]) {
                window.location.href = pages[page];
            }
        }

        // 审核弹窗相关函数
        function openAuditModal(customerName) {
            document.getElementById('audit-modal').style.display = 'block';
        }

        function closeAuditModal() {
            document.getElementById('audit-modal').style.display = 'none';
        }

        function approveAudit() {
            alert('审核通过！');
            closeAuditModal();
        }

        function rejectAudit() {
            alert('审核驳回！');
            closeAuditModal();
        }

        // 处理审核操作
        function handleAudit(customerName, currentAuditOrg, canAudit) {
            if (canAudit) {
                // 当前机构有审核权限，打开审核弹窗
                openAuditModal(customerName);
            }
        }

        // 客户经理审核流程数据
        const customerProcessData = {
            'CM001': {
                applicant: '黄陵县店头营业所',
                submitTime: '2025-06-20 15:48:31',
                customerName: '王莹',
                customerPhone: '18740618843',
                steps: [
                    {
                        level: '网点审核',
                        institution: '黄陵县店头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：同意添加该客户经理',
                        time: '2025-06-20 15:50:15',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '黄陵县分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM002': {
                applicant: '城固县龙头营业所',
                submitTime: '2025-06-20 15:41:41',
                customerName: '胡丽娜',
                customerPhone: '17762243634',
                steps: [
                    {
                        level: '网点审核',
                        institution: '城固县龙头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：符合客户经理条件',
                        time: '2025-06-20 15:45:20',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '城固县分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：同意添加',
                        time: '2025-06-20 16:10:30',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汉中市分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM003': {
                applicant: '德保县东关营业所',
                submitTime: '2025-06-20 15:27:08',
                customerName: '梁芳丽',
                customerPhone: '18074840225',
                steps: [
                    {
                        level: '网点审核',
                        institution: '德保县东关营业所',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM004': {
                applicant: '黄陵县店头营业所',
                submitTime: '2025-06-20 15:49:55',
                customerName: '马小明',
                customerPhone: '18221860290',
                steps: [
                    {
                        level: '网点审核',
                        institution: '黄陵县店头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：同意添加该客户经理',
                        time: '2025-06-20 15:52:30',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '黄陵县分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：符合条件，同意添加',
                        time: '2025-06-20 16:15:20',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '延安市分公司',
                        status: 'rejected',
                        statusText: '审核驳回',
                        content: '意见：客户经理资质不符合要求，需重新申请',
                        time: '2025-06-20 17:30:45',
                        operator: '市级审核员'
                    },
                    {
                        level: '网点审核',
                        institution: '黄陵县店头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：已完善资质材料，重新申请',
                        time: '2025-06-21 09:20:15',
                        operator: '营业所主任'
                    },
                    {
                        level: '市级审核',
                        institution: '延安市分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：资质材料完善，同意添加',
                        time: '2025-06-21 14:45:30',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '陕西省分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过',
                        time: '2025-06-21 16:20:00',
                        operator: '省级审核员'
                    }
                ]
            },
            'CM005': {
                applicant: '城固县天明营业所',
                submitTime: '2025-06-20 15:42:41',
                customerName: '胡丽娜',
                customerPhone: '17762243634',
                steps: [
                    {
                        level: '网点审核',
                        institution: '城固县天明营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：客户经理条件符合要求',
                        time: '2025-06-20 15:50:20',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '城固县分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：同意添加',
                        time: '2025-06-20 16:25:40',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汉中市分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：审核通过',
                        time: '2025-06-20 17:10:15',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '陕西省分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过',
                        time: '2025-06-20 18:30:25',
                        operator: '省级审核员'
                    }
                ]
            },
            'CM006': {
                applicant: '西安市雁塔营业所',
                submitTime: '2025-06-20 14:15:22',
                customerName: '李明华',
                customerPhone: '13567892345',
                steps: [
                    {
                        level: '网点审核',
                        institution: '西安市雁塔营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：客户经理资质良好，同意添加',
                        time: '2025-06-20 14:20:30',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '西安市雁塔区分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：符合客户经理条件',
                        time: '2025-06-20 15:10:15',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '西安市分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM007': {
                applicant: '宝鸡市金台营业所',
                submitTime: '2025-06-20 13:42:18',
                customerName: '张小红',
                customerPhone: '15698734521',
                steps: [
                    {
                        level: '网点审核',
                        institution: '宝鸡市金台营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：工作经验丰富，同意添加',
                        time: '2025-06-20 13:50:45',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '宝鸡市金台区分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM008': {
                applicant: '咸阳市秦都营业所',
                submitTime: '2025-06-20 12:28:35',
                customerName: '赵建国',
                customerPhone: '18923456789',
                // 多流程数据结构
                hasMultipleFlows: true,
                currentFlowIndex: 2, // 当前流程索引
                flows: [
                    // 第一次历史流程（已驳回）
                    {
                        flowId: 'CM008-001',
                        flowName: '',
                        submitTime: '2025-06-19 14:20:15',
                        status: 'rejected',
                        steps: [
                            {
                                level: '网点审核',
                                institution: '咸阳市秦都营业所',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：初步审核通过，符合基本条件',
                                time: '2025-06-19 14:25:30',
                                operator: '营业所主任'
                            },
                            {
                                level: '区县审核',
                                institution: '咸阳市秦都区分公司',
                                status: 'rejected',
                                statusText: '审核驳回',
                                content: '意见：客户经理资质材料不完整，需要补充相关证明',
                                time: '2025-06-19 16:40:20',
                                operator: '区县审核员'
                            }
                        ]
                    },
                    // 第二次历史流程（已驳回）
                    {
                        flowId: 'CM008-002',
                        flowName: '',
                        submitTime: '2025-06-20 12:28:35',
                        status: 'rejected',
                        steps: [
                            {
                                level: '网点审核',
                                institution: '咸阳市秦都营业所',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：已补充资质材料，业务能力突出，同意添加',
                                time: '2025-06-20 12:35:20',
                                operator: '营业所主任'
                            },
                            {
                                level: '区县审核',
                                institution: '咸阳市秦都区分公司',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：资质材料完善，审核通过',
                                time: '2025-06-20 14:20:30',
                                operator: '区县审核员'
                            },
                            {
                                level: '市级审核',
                                institution: '咸阳市分公司',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：市级审核通过',
                                time: '2025-06-20 16:45:15',
                                operator: '市级审核员'
                            },
                            {
                                level: '省级审核',
                                institution: '陕西省分公司',
                                status: 'rejected',
                                statusText: '审核驳回',
                                content: '意见：客户经理业务经验不足，需要进一步培训后重新申请',
                                time: '2025-06-20 18:30:45',
                                operator: '省级审核员'
                            }
                        ]
                    },
                    // 当前流程（审核中）
                    {
                        flowId: 'CM008-003',
                        flowName: '',
                        submitTime: '2025-06-21 09:15:30',
                        status: 'reviewing',
                        steps: [
                            {
                                level: '网点审核',
                                institution: '咸阳市秦都营业所',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：已完成相关培训，业务能力提升明显，同意重新申请',
                                time: '2025-06-21 09:20:15',
                                operator: '营业所主任'
                            },
                            {
                                level: '区县审核',
                                institution: '咸阳市秦都区分公司',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：培训效果良好，同意继续审核',
                                time: '2025-06-21 10:30:20',
                                operator: '区县审核员'
                            },
                            {
                                level: '市级审核',
                                institution: '咸阳市分公司',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：重新审核通过',
                                time: '2025-06-21 14:15:40',
                                operator: '市级审核员'
                            },
                            {
                                level: '省级审核',
                                institution: '陕西省分公司',
                                status: 'current',
                                statusText: '审核中',
                                content: '意见：--',
                                time: null,
                                operator: null
                            }
                        ]
                    }
                ],
                // 为了保持向后兼容，保留原有的steps结构（指向当前流程）
                steps: [
                    {
                        level: '网点审核',
                        institution: '咸阳市秦都营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：已完成相关培训，业务能力提升明显，同意重新申请',
                        time: '2025-06-21 09:20:15',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '咸阳市秦都区分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：培训效果良好，同意继续审核',
                        time: '2025-06-21 10:30:20',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '咸阳市分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：重新审核通过',
                        time: '2025-06-21 14:15:40',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '陕西省分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            }
        };

        // 显示流程查看弹窗
        function showProcessModal(customerId) {
            const modal = document.getElementById('process-modal');
            const data = customerProcessData[customerId];
            
            if (!data) {
                alert('未找到该客户经理的流程信息');
                return;
            }
            
            // 检查是否有多个流程
            if (data.hasMultipleFlows) {
                // 显示标签页
                document.getElementById('flow-tabs').style.display = 'block';
                
                // 初始化标签页事件
                initFlowTabs(customerId);
                
                // 默认显示当前流程
                showCurrentFlow(customerId);
            } else {
                // 隐藏标签页
                document.getElementById('flow-tabs').style.display = 'none';
                
                // 生成单个流程
                generateCustomerProcessFlow(data);
            }
            
            // 显示弹窗
            modal.style.display = 'block';
            
            // 点击遮罩关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProcessModal();
                }
            });
        }

        // 关闭流程查看弹窗
        function closeProcessModal() {
            const modal = document.getElementById('process-modal');
            modal.style.display = 'none';
            
            // 隐藏标签页
            document.getElementById('flow-tabs').style.display = 'none';
            
            // 清理标签页状态
            const tabs = document.querySelectorAll('.flow-tab-item');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            const currentTab = document.querySelector('.flow-tab-item[data-tab="current"]');
            if (currentTab) {
                currentTab.classList.add('active');
            }
        }

        // 初始化多流程标签页
        function initFlowTabs(customerId) {
            const tabs = document.querySelectorAll('.flow-tab-item');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // 添加当前标签活动状态
                    this.classList.add('active');
                    
                    // 根据标签类型显示对应内容
                    const tabType = this.dataset.tab;
                    if (tabType === 'current') {
                        showCurrentFlow(customerId);
                    } else if (tabType === 'history') {
                        showHistoryFlows(customerId);
                    }
                });
            });
        }

        // 显示当前流程
        function showCurrentFlow(customerId) {
            const data = customerProcessData[customerId];
            if (!data || !data.hasMultipleFlows) return;
            
            const currentFlow = data.flows[data.currentFlowIndex];
            const flowData = {
                applicant: data.applicant,
                submitTime: currentFlow.submitTime,
                customerName: data.customerName,
                customerPhone: data.customerPhone,
                steps: currentFlow.steps,
                flowName: currentFlow.flowName
            };
            
            generateCustomerProcessFlow(flowData);
        }

        // 显示历史流程
        function showHistoryFlows(customerId) {
            const data = customerProcessData[customerId];
            if (!data || !data.hasMultipleFlows) return;
            
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 获取所有历史流程（非当前流程）
            const historyFlows = data.flows.filter((flow, index) => index !== data.currentFlowIndex);
            
            if (historyFlows.length === 0) {
                timeline.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 40px;">暂无历史流程</div>';
                return;
            }
            
            // 为每个历史流程创建折叠组件
            historyFlows.forEach((flow, index) => {
                const originalIndex = data.flows.findIndex(f => f.flowId === flow.flowId);
                createHistoryFlowItem(data, flow, originalIndex, index + 1);
            });
        }

        // 创建历史流程折叠项
        function createHistoryFlowItem(data, flow, originalIndex, displayIndex) {
            const timeline = document.getElementById('process-timeline');
            
            // 创建折叠容器
            const flowItem = document.createElement('div');
            flowItem.className = 'history-flow-item';
            flowItem.id = `history-flow-${originalIndex}`;
            
            // 获取流程状态信息
            const statusInfo = getFlowStatusInfo(flow);
            
            // 创建折叠头部
            const header = document.createElement('div');
            header.className = 'history-flow-header';
            header.onclick = () => toggleHistoryFlow(originalIndex);
            
            header.innerHTML = `
                <div class="history-flow-header-info">
                    <div class="history-flow-title">第${displayIndex}次申请</div>
                    <div class="history-flow-meta">
                        <span>提交时间：${flow.submitTime}</span>
                        <span class="history-flow-status ${statusInfo.class}">${statusInfo.text}</span>
                    </div>
                </div>
                <div class="history-flow-toggle">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 9l6 6 6-6"/>
                    </svg>
                </div>
            `;
            
            // 创建折叠内容
            const content = document.createElement('div');
            content.className = 'history-flow-content';
            content.id = `history-flow-content-${originalIndex}`;
            
            const timelineContainer = document.createElement('div');
            timelineContainer.className = 'history-flow-timeline';
            
            content.appendChild(timelineContainer);
            
            // 组装折叠项
            flowItem.appendChild(header);
            flowItem.appendChild(content);
            timeline.appendChild(flowItem);
            
            // 生成流程内容（但不显示）
            generateHistoryFlowContent(data, flow, timelineContainer);
        }
        
        // 获取流程状态信息
        function getFlowStatusInfo(flow) {
            switch(flow.status) {
                case 'rejected':
                    return { text: '已驳回', class: 'rejected' };
                case 'completed':
                    return { text: '已完成', class: 'completed' };
                case 'reviewing':
                    return { text: '审核中', class: 'reviewing' };
                default:
                    return { text: '未知状态', class: 'pending' };
            }
        }
        
        // 切换历史流程展开/收起状态
        function toggleHistoryFlow(flowIndex) {
            const header = document.querySelector(`#history-flow-${flowIndex} .history-flow-header`);
            const content = document.querySelector(`#history-flow-content-${flowIndex}`);
            
            if (!header || !content) return;
            
            const isExpanded = header.classList.contains('expanded');
            
            if (isExpanded) {
                // 收起
                header.classList.remove('expanded');
                content.classList.remove('expanded');
            } else {
                // 展开
                header.classList.add('expanded');
                content.classList.add('expanded');
            }
        }
        
        // 生成历史流程内容
        function generateHistoryFlowContent(data, flow, container) {
            const flowData = {
                applicant: data.applicant,
                submitTime: flow.submitTime,
                customerName: data.customerName,
                customerPhone: data.customerPhone,
                steps: flow.steps,
                flowName: flow.flowName
            };
            
            // 清空容器
            container.innerHTML = '';
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>发起申请</span>
                            <span class="flow-status completed">已提交</span>
                        </div>
                    </div>
                    <div class="flow-institution">${flowData.applicant}</div>
                    <div class="flow-description">申请添加客户经理：${flowData.customerName}（${flowData.customerPhone}）</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${flowData.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${flowData.applicant}
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(startItem);
            
            // 添加审核节点
            flowData.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${step.statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                container.appendChild(flowItem);
            });
            
            // 添加结束节点 - 根据流程状态决定结束节点
            const lastStep = flowData.steps[flowData.steps.length - 1];
            const lastStepRejected = lastStep && lastStep.status === 'rejected';
            const allCompleted = flowData.steps.every(step => step.status === 'completed' || step.status === 'rejected');
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            
            if (lastStepRejected) {
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
            } else if (allCompleted) {
                endStatus = 'completed';
                endText = '流程完成';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${lastStepRejected ? '由于审核驳回，流程终止' : endText}</div>
                </div>
            `;
            container.appendChild(endItem);
        }

        // 生成客户经理审核流程HTML
        function generateCustomerProcessFlow(data) {
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 添加流程标题（如果有）
            if (data.flowName) {
                const flowTitle = document.createElement('div');
                flowTitle.style.cssText = 'padding: 0 0 20px 0; font-size: 16px; font-weight: 500; color: var(--primary-blue); border-bottom: 1px solid var(--border-color-light); margin-bottom: 20px;';
                flowTitle.textContent = data.flowName;
                timeline.appendChild(flowTitle);
            }
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>发起申请</span>
                            <span class="flow-status completed">已提交</span>
                        </div>
                    </div>
                    <div class="flow-institution">${data.applicant}</div>
                    <div class="flow-description">申请添加客户经理：${data.customerName}（${data.customerPhone}）</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${data.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${data.applicant}
                        </div>
                    </div>
                </div>
            `;
            timeline.appendChild(startItem);
            
            // 添加审核节点
            data.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                let statusText = step.statusText;
                if (step.status === 'current') {
                    statusText = '审核中';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                timeline.appendChild(flowItem);
            });
            
            // 添加结束节点
            // 检查最后一个步骤的状态来决定结束节点状态
            const lastStep = data.steps[data.steps.length - 1];
            const allCompleted = data.steps.every(step => step.status === 'completed' || step.status === 'rejected');
            const hasCurrentStep = data.steps.some(step => step.status === 'current');
            const lastStepRejected = lastStep && lastStep.status === 'rejected';
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            
            if (lastStepRejected) {
                // 如果最后一个步骤是驳回，则流程终止
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
            } else if (allCompleted && !hasCurrentStep) {
                // 如果所有步骤都完成（包括中间可能的驳回），且没有当前审核中的步骤，则审核完成
                endStatus = 'completed';
                endText = '流程结束';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${endText}</div>
                </div>
            `;
            timeline.appendChild(endItem);
        }





        // 配置弹窗相关函数
        function openConfigModal() {
            document.getElementById('config-modal').style.display = 'block';
        }

        function closeConfigModal() {
            document.getElementById('config-modal').style.display = 'none';
        }

        function saveConfig() {
            // 获取审核规则配置
            const auditConfig = {
                province: document.getElementById('audit-province').checked,
                city: document.getElementById('audit-city').checked,
                county: document.getElementById('audit-county').checked,
                outlet: document.getElementById('audit-outlet').checked
            };

            // 验证至少选择一个审核机构
            const hasSelection = Object.values(auditConfig).some(val => val);

            if (!hasSelection) {
                alert('至少需要选择一个审核机构！');
                return;
            }

            // 保存配置（这里可以调用后端API）
            console.log('客户经理审核规则配置:', auditConfig);

            alert('审核规则配置保存成功！');
            closeConfigModal();
        }

        // 为审核按钮添加点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 强制设置当前页面的菜单为激活状态
            const currentPage = 'team'; 
            const navItems = document.querySelectorAll('.nav-section .nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('onclick') === `switchToPage('${currentPage}')`) {
                    item.classList.add('active');
                }
            });

            const ruleOptions = document.querySelectorAll('.rule-option');
            ruleOptions.forEach(option => {
                option.addEventListener('click', (event) => {
                    const checkbox = option.querySelector('input[type="checkbox"]');
                    if (event.target.type !== 'checkbox') {
                        checkbox.checked = !checkbox.checked;
                    }
                    if (checkbox.checked) {
                        option.classList.add('active');
                    } else {
                        option.classList.remove('active');
                    }
                });
            });
        });

        // 更新复选框样式
        function updateCheckboxStyle(checkbox) {
            const label = checkbox.closest('.checkbox-label');
            if (checkbox.checked) {
                label.style.borderColor = 'var(--primary-blue)';
                label.style.background = 'var(--primary-blue-light)';
            } else {
                label.style.borderColor = 'var(--border-color)';
                label.style.background = 'transparent';
            }
        }

        // 点击遮罩关闭弹窗
        document.getElementById('audit-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuditModal();
            }
        });

        document.getElementById('config-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeConfigModal();
            }
        });

        document.getElementById('process-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProcessModal();
            }
        });
    </script>
</body>
</html> 