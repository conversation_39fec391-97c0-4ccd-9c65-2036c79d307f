<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 审核中心管理系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        .btn-danger {
            background: var(--error-red);
            border-color: var(--error-red);
            color: #ffffff;
        }

        .btn-danger:hover {
            background: #ff7875;
            border-color: #ff7875;
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-approved {
            background: #dcfce7;
            color: var(--success-green);
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-reviewing {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .status-normal {
            background: #dcfce7;
            color: var(--success-green);
        }

        /* 审核类型标签 */
        .audit-type-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .audit-type-team {
            background: #e6f4ff;
            color: var(--primary-blue);
        }

        .audit-type-equipment {
            background: #f0f9ff;
            color: #0ea5e9;
        }

        .audit-type-onboarding {
            background: #fef3c7;
            color: #d97706;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            overflow-y: auto;
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 800px;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 详情页面样式 */
        .detail-section {
            margin-bottom: 24px;
        }

        .detail-section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .detail-label {
            font-weight: 500;
            color: var(--text-secondary);
            width: 120px;
            flex-shrink: 0;
        }

        .detail-value {
            color: var(--text-primary);
            flex: 1;
        }

        /* 流程查看弹窗样式 */
        .process-modal-content {
            width: 700px;
            max-width: 95vw;
            max-height: 90vh;
            height: auto;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            margin: 5vh auto;
            overflow: hidden;
        }

        .process-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            max-height: calc(90vh - 120px);
            min-height: 0;
        }

        .process-flow {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
        }

        .process-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 20px;
            width: 2px;
            background: var(--border-color-light);
        }

        .flow-item {
            position: relative;
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
        }

        .flow-item:last-child {
            margin-bottom: 0;
        }

        .flow-node {
            position: absolute;
            left: -28px;
            top: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--bg-container);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-node.start {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.completed {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.current {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .flow-node.pending {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-node.rejected {
            background: var(--error-red);
            border-color: var(--error-red);
        }

        .flow-node.end {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-content {
            flex: 1;
            min-width: 0;
        }

        .flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .flow-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .flow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .flow-status.current {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .flow-status.pending {
            background: #f5f5f5;
            color: var(--text-secondary);
        }

        .flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .flow-institution {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .flow-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .flow-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-disabled);
        }

        .flow-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 复选框标签样式 */
        .checkbox-label {
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .checkbox-label:hover {
            border-color: var(--primary-blue);
            background: var(--primary-blue-light);
        }

        .checkbox-label input[type="checkbox"]:checked + span {
            color: var(--primary-blue);
            font-weight: 500;
        }

        /* 多流程标签页样式 */
        .flow-tabs {
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .flow-tab-header {
            display: flex;
            padding: 0 24px;
        }

        .flow-tab-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 400;
            transition: all 0.2s ease;
            position: relative;
        }

        .flow-tab-item:hover {
            color: var(--primary-blue);
        }

        .flow-tab-item.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
            font-weight: 500;
        }

        .flow-tab-item span {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 历史流程折叠组件样式 */
        .history-flow-item {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .history-flow-item:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        .history-flow-header {
            padding: 16px 20px;
            background: #fafafa;
            border-bottom: 1px solid var(--border-color-light);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
            user-select: none;
        }

        .history-flow-header:hover {
            background: #f0f0f0;
        }

        .history-flow-header.expanded {
            background: var(--primary-blue-light);
            border-bottom-color: var(--primary-blue);
        }

        .history-flow-header-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .history-flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .history-flow-meta {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .history-flow-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .history-flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .history-flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .history-flow-status.reviewing {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .history-flow-toggle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--bg-container);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .history-flow-toggle svg {
            width: 12px;
            height: 12px;
            color: var(--text-secondary);
            transition: transform 0.3s ease;
        }

        .history-flow-header.expanded .history-flow-toggle {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .history-flow-header.expanded .history-flow-toggle svg {
            color: white;
            transform: rotate(180deg);
        }

        .history-flow-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .history-flow-content.expanded {
            max-height: 2000px;
        }

        .history-flow-timeline {
            padding: 20px;
            position: relative;
            padding-left: 60px;
        }

        .history-flow-timeline::before {
            content: '';
            position: absolute;
            left: 40px;
            top: 0;
            bottom: 20px;
            width: 2px;
            background: var(--border-color-light);
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核中心</div>
                <div class="nav-item active">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    审核中心
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    费率审核
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核中心</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">审核中心</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 审核中心页面 -->
        <div class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">审核中心</h1>
                </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">申请人</label>
                            <input type="text" placeholder="请输入申请人" class="form-input" id="applicant-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">市级分公司</label>
                            <input type="text" placeholder="请输入" class="form-input" id="merchant-id-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">区县分公司</label>
                            <input type="text" placeholder="请输入" class="form-input" id="merchant-id-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">营业所</label>
                            <input type="text" placeholder="请输入" class="form-input" id="merchant-id-input">
                        </div>
                      
                        <div class="form-group">
                            <label class="form-label">审核类型</label>
                            <select class="form-select" id="audit-type-select">
                                <option value="">全部类型</option>
                                <option value="team">团队审核</option>
                                <option value="equipment">设备审核</option>
                                <option value="onboarding">进件审核</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-select" id="audit-status-select">
                                <option value="">全部状态</option>
                                <option value="pending">待审核</option>
                                <option value="reviewing">审核中</option>
                                <option value="approved">审核通过</option>
                                <option value="rejected">审核驳回</option>
                            </select>
                        </div>
                    
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; align-items: end;">
                        <div class="form-group">
                            <label class="form-label">申请时间</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="date" value="2025-01-06" class="form-input" style="flex: 1;">
                                <span style="color: var(--text-secondary);">至</span>
                                <input type="date" value="2025-01-06" class="form-input" style="flex: 1;">
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary" onclick="searchAuditRecords()">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-default" onclick="resetSearch()">重置</button>
                        </div>
                    </div>
                </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    <button class="btn btn-default" onclick="openConfigModal()">
                        <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                        </svg>
                        审核配置
                    </button>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>申请单号</th>
                                
                                <th>申请人</th>
                                <th>市级分公司</th>
                                <th>区县分公司</th>
                                <th>营业所</th>
                                <th>审核类型</th>
                                <th>审核状态</th>
                                <th>申请时间</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="audit-table-body">
                            <!-- 团队审核数据 -->
                            <tr>
                                <td>TD2025077001</td>
                                
                                <td>王莹</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-team">团队审核</span></td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>2025-01-06 15:48:31</td>
                                <td>2025-01-06 15:48:31</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('CM20250106001', 'team')">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('CM20250106001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TD2025077002</td>
                                
                                <td>胡丽娜</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-team">团队审核</span></td>
                                <td><span class="status-tag status-approved">审核通过</span></td>
                                <td>2025-01-06 15:41:41</td>
                                <td>2025-01-06 15:48:31</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('CM20250106002', 'team')">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('CM20250106002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 设备审核数据 -->
                            <tr>
                                <td>TD2025077003</td>
                                
                                <td>张三</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-equipment">设备审核</span></td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>2025-01-06 14:20:15</td>
                                <td>2025-01-06 14:20:15</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('EQ20250106001', 'equipment')">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ20250106001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TD2025077004</td>
                                
                                <td>李四</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-equipment">设备审核</span></td>
                                <td><span class="status-tag status-approved">审核通过</span></td>
                                <td>2025-01-06 13:15:22</td>
                                <td>2025-01-06 14:20:15</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('EQ20250106002', 'equipment')">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ20250106002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 进件审核数据 -->
                            <tr>
                                <td>TD2025077005</td>
                               
                                <td>王五</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-onboarding">进件审核</span></td>
                                <td><span class="status-tag status-reviewing">审核中</span></td>
                                <td>2025-01-06 12:30:45</td>
                                <td>2025-01-06 11:45:18</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('OB20250106001', 'onboarding')">审核</button>
                                        <button class="btn-link" onclick="showProcessModal('OB20250106001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>TD2025077006</td>
                                
                                <td>赵六</td>
                                <td>深圳市分公司</td>
                                <td>深圳市南山区分公司</td>
                                <td>深圳南山区南湖大厦营业所</td>
                                <td><span class="audit-type-tag audit-type-onboarding">进件审核</span></td>
                                <td><span class="status-tag status-rejected">审核驳回</span></td>
                                <td>2025-01-06 11:45:18</td>
                                <td>2025-01-06 11:45:18</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="showDetailModal('OB20250106002', 'onboarding')">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('OB20250106002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 6 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">10条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="detail-modal-title">审核详情</h3>
                <button class="modal-close" onclick="closeDetailModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="detail-modal-body">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeDetailModal()">取消</button>
                <button class="btn btn-danger" onclick="showRejectModal()">驳回</button>
                <button class="btn btn-primary" onclick="showApproveModal()">审核通过</button>
            </div>
        </div>
    </div>

    <!-- 流程查看弹窗 -->
    <div id="process-modal" class="modal">
        <div class="modal-content process-modal-content">
            <div class="modal-header">
                <h3 class="modal-title">查看流程</h3>
                <button class="modal-close" onclick="closeProcessModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <!-- 多流程标签页 -->
            <div id="flow-tabs" class="flow-tabs" style="display: none;">
                <div class="flow-tab-header">
                    <div class="flow-tab-item active" data-tab="current">
                        <span>当前流程</span>
                    </div>
                    <div class="flow-tab-item" data-tab="history">
                        <span>历史流程</span>
                    </div>
                </div>
            </div>
            <div class="modal-body process-modal-body">
                <div class="process-flow" id="process-timeline">
                    <!-- 动态生成的流程内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const auditData = {
            // 团队审核数据
            'CM20250106001': {
                type: 'team',
                id: 'CM20250106001',
                merchantName: '黄陵县店头营业所',
                applicant: '王莹',
                phone: '***********',
                position: '客户经理',
                city: '黄陵市分公司',
                department: '黄陵县分公司',
                branch: '黄陵县店头营业所',
                applyTime: '2025-01-06 15:48:31',
                updateTime: '2025-01-06 15:48:31',
                reason: '-',
                status: '审核中'
            },
            'CM20250106002': {
                type: 'team',
                id: 'CM20250106002',
                merchantName: '城固县龙头营业所',
                applicant: '胡丽娜',
                phone: '17762243634',
                position: '客户经理',
                city: '城固市分公司',
                department: '城固县分公司',
                branch: '城固县龙头营业所',
                applyTime: '2025-01-06 15:41:41',
                updateTime: '2025-01-06 15:41:41',
                reason: '-',
                status: '审核通过'
            },
            // 设备审核数据
            'EQ20250106001': {
                type: 'equipment',
                id: 'TD2025077003',
                merchantId: 'EQ20250106001',
                merchantName: '潮州市邮政便民服务点',
                applicant: '张三',
                city: '汉中市分公司',
                department: '城固县分公司',
                branch: '城固县龙头营业所',
                equipmentType: '码牌',
                equipmentModel: 'A920Pro',
                equipmentSN: 'SN20250106001',
                Number: 30,
                applyReason: '商户申请码牌',
                applyTime: '2025-01-06 14:20:15',
                updateTime: '2025-01-06 14:20:15',
                reason: '-',
                status: '审核中'
            },
            'EQ20250106002': {
                type: 'equipment',
                id: 'TD2025077004',
                merchantId: 'EQ20250106002',
                merchantName: '汕头市邮政营业厅',
                applicant: '李四',
                city: '汉中市分公司',
                department: '城固县分公司',
                branch: '城固县龙头营业所',
                equipmentType: '码牌',
                equipmentModel: 'A920Pro',
                equipmentSN: 'SN20250106002',
                Number: 50,
                applyReason: '商户申请码牌',
                applyTime: '2025-01-06 13:15:22',
                updateTime: '2025-01-06 13:15:22',
                reason: '-',
                status: '审核通过'
            },
            // 进件审核数据
            'OB20250106001': {
                type: 'onboarding',
                id: 'OB20250106001',
                merchantName: '小王杂货店',
                applicant: '王五',
                businessType: '个体工商户',
                businessLicense: '91440000123456789X',
                legalPerson: '王五',
                contactPhone: '***********',
                idCard: '******************',
                bankid: '6221881771018722564',
                city: '潮州市分公司',
                department: '城固县分公司',
                branch: '城固县龙头营业所',
                businessAddress: '潮州市湘桥区XX路XX号',
                applyTime: '2025-01-06 12:30:45',
                updateTime: '2025-01-06 12:30:45',
                applyputtime: '2025-01-08 11:45:18-2025-01-11 11:45:18',
                limitreason : '网点经理家属，不允许进件',
                reason: '特殊申请进件',
                rejectedReason: '-',
                status: '审核中'
            },
            'OB20250106002': {
                type: 'onboarding',
                id: 'OB20250106002',
                merchantName: '小赵烧烤店',
                applicant: '赵六',
                businessType: '个人商户',
                businessLicense: '91440000987654321Y',
                legalPerson: '赵六',
                idCard: '******************',
                bankid: '6221881771018722464',
                city: '潮州市分公司',
                department: '城固县分公司',
                branch: '城固县龙头营业所',
                contactPhone: '***********',
                businessAddress: '梅州市梅江区XX路XX号',
                applyTime: '2025-01-06 11:45:18',
                updateTime: '2025-01-06 11:45:18',
                applyputtime: '2025-01-06 11:45:18-2025-01-09 11:45:18',
                limitreason : '进件黑名单',
                reason: '具备进件条件，申请进件',
                rejectedReason: '条件不满足，不允许进件',
                status: '审核驳回'
            }
        };

        // 流程数据
        const processData = {
            'CM20250106001': {
                type: 'team',
                applicant: '黄陵县店头营业所',
                submitTime: '2025-01-06 15:48:31',
                customerName: '王莹',
                customerPhone: '***********',
                // 添加多流程支持
                hasMultipleFlows: true,
                currentFlowIndex: 1, // 当前流程索引
                flows: [
                    // 第一次历史流程（已驳回）
                    {
                        flowId: 'CM20250106001-001',
                        flowName: '',
                        submitTime: '2025-01-05 14:20:15',
                        status: 'rejected',
                        steps: [
                            {
                                level: '网点审核',
                                institution: '黄陵县店头营业所',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：初步审核通过，符合基本条件',
                                time: '2025-01-05 14:25:30',
                                operator: '营业所主任'
                            },
                            {
                                level: '区县审核',
                                institution: '黄陵县分公司',
                                status: 'rejected',
                                statusText: '审核驳回',
                                content: '意见：客户经理资质材料不完整，需要补充相关证明',
                                time: '2025-01-05 16:40:20',
                                operator: '区县审核员'
                            }
                        ]
                    },
                    // 当前流程（审核中）
                    {
                        flowId: 'CM20250106001-002',
                        flowName: '',
                        submitTime: '2025-01-06 15:48:31',
                        status: 'reviewing',
                        steps: [
                            {
                                level: '网点审核',
                                institution: '黄陵县店头营业所',
                                status: 'completed',
                                statusText: '审核通过',
                                content: '意见：已补充资质材料，同意添加该客户经理',
                                time: '2025-01-06 15:50:15',
                                operator: '营业所主任'
                            },
                            {
                                level: '区县审核',
                                institution: '黄陵县分公司',
                                status: 'current',
                                statusText: '审核中',
                                content: '意见：--',
                                time: null,
                                operator: null
                            }
                        ]
                    }
                ],
                // 为了保持向后兼容，保留原有的steps结构（指向当前流程）
                steps: [
                    {
                        level: '网点审核',
                        institution: '黄陵县店头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：已补充资质材料，同意添加该客户经理',
                        time: '2025-01-06 15:50:15',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '黄陵县分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'CM20250106002': {
                type: 'team',
                applicant: '城固县龙头营业所',
                submitTime: '2025-01-06 15:41:41',
                customerName: '胡丽娜',
                customerPhone: '17762243634',
                steps: [
                    {
                        level: '网点审核',
                        institution: '城固县龙头营业所',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：符合客户经理条件',
                        time: '2025-01-06 15:45:20',
                        operator: '营业所主任'
                    },
                    {
                        level: '区县审核',
                        institution: '城固县分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：同意添加',
                        time: '2025-01-06 16:10:30',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汉中市分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：审核通过',
                        time: '2025-01-06 17:15:45',
                        operator: '市级审核员'
                    }
                ]
            },
            'EQ20250106001': {
                type: 'equipment',
                applicant: '潮州市邮政便民服务点',
                submitTime: '2025-01-06 14:20:15',
                merchantName: '潮州市邮政便民服务点',
                equipmentType: 'POS机',
                steps: [
                    {
                        level: '区县审核',
                        institution: '潮州市湘桥区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：设备申请合理，同意配置',
                        time: '2025-01-06 14:25:30',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '潮州市邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'EQ20250106002': {
                type: 'equipment',
                applicant: '汕头市邮政营业厅',
                submitTime: '2025-01-06 13:15:22',
                merchantName: '汕头市邮政营业厅',
                equipmentType: 'POS机',
                steps: [
                    {
                        level: '区县审核',
                        institution: '汕头市龙湖区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：设备需求合理，同意申请',
                        time: '2025-01-06 13:20:45',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汕头市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过',
                        time: '2025-01-06 14:30:20',
                        operator: '市级审核员'
                    }
                ]
            },
            'OB20250106001': {
                type: 'onboarding',
                applicant: '潮州市邮政便民服务点',
                submitTime: '2025-01-06 12:30:45',
                merchantName: '潮州市邮政便民服务点',
                businessType: '邮政代办',
                steps: [
                    {
                        level: '市级审核',
                        institution: '潮州市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：初步审核通过',
                        time: '2025-01-06 12:35:20',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'OB20250106002': {
                type: 'onboarding',
                applicant: '梅州市邮政代办点',
                submitTime: '2025-01-06 11:45:18',
                merchantName: '梅州市邮政代办点',
                businessType: '邮政代办',
                steps: [
                    {
                        level: '市级审核',
                        institution: '梅州市邮政分公司',
                        status: 'rejected',
                        statusText: '审核驳回',
                        content: '意见：资质材料不完整，需要补充相关证明',
                        time: '2025-01-06 11:50:30',
                        operator: '市级审核员'
                    }
                ]
            }
        };

        // 显示详情弹窗
        function showDetailModal(id, type) {
            const modal = document.getElementById('detail-modal');
            const title = document.getElementById('detail-modal-title');
            const body = document.getElementById('detail-modal-body');
            const data = auditData[id];

            if (!data) {
                alert('未找到相关数据');
                return;
            }

            // 设置标题
            const typeNames = {
                'team': '团队审核',
                'equipment': '设备审核',
                'onboarding': '进件审核'
            };
            title.textContent = typeNames[type] + '详情';

            // 根据类型生成不同的详情内容
            let detailContent = '';
            
            if (type === 'team') {
                detailContent = `
                    <div class="detail-section">
                        <h4 class="detail-section-title">审核信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">审核状态：</span>
                                <span class="detail-value">${data.status}</span>
                            </div>
                             <div class="detail-item">
                                <span class="detail-label">申请单号：</span>
                                <span class="detail-value">${data.id}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请人：</span>
                                <span class="detail-value">${data.applicant}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">申请时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">更新时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">驳回原因：</span>
                                <span class="detail-value">${data.reason}</span>
                            </div>
                        </div>
                        <br>
                        <h4 class="detail-section-title">客户经理信息</h4>
                        <div class="detail-grid">
                            
                            <div class="detail-item">
                                <span class="detail-label">姓名：</span>
                                <span class="detail-value">${data.applicant}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">手机号：</span>
                                <span class="detail-value">${data.phone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">市级分公司：</span>
                                <span class="detail-value">${data.city}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">区县分公司：</span>
                                <span class="detail-value">${data.department}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">营业所：</span>
                                <span class="detail-value">${data.branch}</span>
                            </div>
                           
                        </div>
                    </div>
                `;
            } else if (type === 'equipment') {
                detailContent = `
                    <div class="detail-section">
                        <div class="detail-section">
                        <h4 class="detail-section-title">审核信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">审核状态：</span>
                                <span class="detail-value">${data.status}</span>
                            </div>
                             <div class="detail-item">
                                <span class="detail-label">申请单号：</span>
                                <span class="detail-value">${data.id}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请人：</span>
                                <span class="detail-value">${data.applicant}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">市级分公司：</span>
                                <span class="detail-value">${data.city}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">区县分公司：</span>
                                <span class="detail-value">${data.department}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">营业所：</span>
                                <span class="detail-value">${data.branch}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">更新时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">驳回原因：</span>
                                <span class="detail-value">${data.reason}</span>
                            </div>
                        </div>
                        <br>
                        <h4 class="detail-section-title">设备申请信息</h4>
                        <div class="detail-grid">
                            
                            <div class="detail-item">
                                <span class="detail-label">商户号：</span>
                                <span class="detail-value">${data.merchantId}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">商户名称：</span>
                                <span class="detail-value">${data.merchantName}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">设备类型：</span>
                                <span class="detail-value">${data.equipmentType}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请数量上限：</span>
                                <span class="detail-value">${data.Number}</span>
                            </div>
                            
                            <div class="detail-item" style="grid-column: 1 / -1;">
                                <span class="detail-label">申请原因：</span>
                                <span class="detail-value">${data.applyReason}</span>
                            </div>
                        </div>
                    </div>
                    
                    </div>
                `;
            } else if (type === 'onboarding') {
                detailContent = `
                    <div class="detail-section">
                        <h4 class="detail-section-title">审核信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">审核状态：</span>
                                <span class="detail-value">${data.status}</span>
                            </div>
                             <div class="detail-item">
                                <span class="detail-label">申请单号：</span>
                                <span class="detail-value">${data.id}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请人：</span>
                                <span class="detail-value">${data.applicant}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">市级分公司：</span>
                                <span class="detail-value">${data.city}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">区县分公司：</span>
                                <span class="detail-value">${data.department}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">营业所：</span>
                                <span class="detail-value">${data.branch}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">更新时间：</span>
                                <span class="detail-value">${data.applyTime}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">驳回原因：</span>
                                <span class="detail-value">${data.rejectedReason}</span>
                            </div>
                        </div>
                        <br>
                        
                        <h4 class="detail-section-title">商户负责人信息</h4>
                        <div class="detail-grid">
                           
                            <div class="detail-item">
                                <span class="detail-label">商户负责人：</span>
                                <span class="detail-value">${data.legalPerson}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">负责人手机号：</span>
                                <span class="detail-value">${data.contactPhone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">负责人身份证号：</span>
                                <span class="detail-value">${data.idCard}</span>
                            </div>
                             <div class="detail-item">
                                <span class="detail-label">银行卡号：</span>
                                <span class="detail-value">${data.bankid}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">营业执照号：</span>
                                <span class="detail-value">${data.businessLicense}</span>
                            </div>
                           
                            
                            
                            
                        </div>
                    </div>
                    <div class="detail-section">
                       <h4 class="detail-section-title">进件申请信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">申请进件时间：</span>
                                <span class="detail-value">${data.applyputtime}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">限制原因：</span>
                                <span class="detail-value">${data.limitreason}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">申请原因：</span>
                                <span class="detail-value">${data.reason}</span>
                            </div>
                            
                        </div> 
                    </div>
                `;
            }

            body.innerHTML = detailContent;
            modal.style.display = 'block';
        }

        // 关闭详情弹窗
        function closeDetailModal() {
            document.getElementById('detail-modal').style.display = 'none';
        }

        // 显示流程查看弹窗
        function showProcessModal(id) {
            const modal = document.getElementById('process-modal');
            const data = processData[id];
            
            if (!data) {
                alert('未找到流程信息');
                return;
            }
            
            // 检查是否有多个流程
            if (data.hasMultipleFlows) {
                // 显示标签页
                document.getElementById('flow-tabs').style.display = 'block';
                
                // 初始化标签页事件
                initFlowTabs(id);
                
                // 默认显示当前流程
                showCurrentFlow(id);
            } else {
                // 隐藏标签页
                document.getElementById('flow-tabs').style.display = 'none';
                
                // 生成单个流程
                generateProcessTimeline(data);
            }
            
            // 显示弹窗
            modal.style.display = 'block';
        }

        // 关闭流程查看弹窗
        function closeProcessModal() {
            const modal = document.getElementById('process-modal');
            modal.style.display = 'none';
            
            // 隐藏标签页
            document.getElementById('flow-tabs').style.display = 'none';
            
            // 清理标签页状态
            const tabs = document.querySelectorAll('.flow-tab-item');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            const currentTab = document.querySelector('.flow-tab-item[data-tab="current"]');
            if (currentTab) {
                currentTab.classList.add('active');
            }
        }

        // 生成流程时间线
        function generateProcessTimeline(data) {
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';

            // 添加流程标题（如果有）
            if (data.flowName) {
                const flowTitle = document.createElement('div');
                flowTitle.style.cssText = 'padding: 0 0 20px 0; font-size: 16px; font-weight: 500; color: var(--primary-blue); border-bottom: 1px solid var(--border-color-light); margin-bottom: 20px;';
                flowTitle.textContent = data.flowName;
                timeline.appendChild(flowTitle);
            }

            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            
            let startDescription = '';
            if (data.type === 'team' || data.customerName) {
                startDescription = `申请添加客户经理：${data.customerName}（${data.customerPhone}）`;
            } else if (data.type === 'equipment') {
                startDescription = `申请设备：${data.equipmentType}`;
            } else if (data.type === 'onboarding') {
                startDescription = `申请进件：${data.businessType}`;
            }
            
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>发起申请</span>
                            <span class="flow-status completed">已提交</span>
                        </div>
                    </div>
                    
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${data.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            提交人：${data.applicant}
                        </div>
                    </div>
                </div>
            `;
            timeline.appendChild(startItem);

            // 添加审核节点
            data.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                let statusText = step.statusText;
                if (step.status === 'current') {
                    statusText = '审核中';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    审核人：${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                timeline.appendChild(flowItem);
            });

            // 添加结束节点
            const lastStep = data.steps[data.steps.length - 1];
            const allCompleted = data.steps.every(step => step.status === 'completed' || step.status === 'rejected');
            const hasCurrentStep = data.steps.some(step => step.status === 'current');
            const lastStepRejected = lastStep && lastStep.status === 'rejected';
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            
            if (lastStepRejected) {
                // 如果最后一个步骤是驳回，则流程终止
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
            } else if (allCompleted && !hasCurrentStep) {
                // 如果所有步骤都完成（包括中间可能的驳回），且没有当前审核中的步骤，则审核完成
                endStatus = 'completed';
                endText = '流程结束';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${endText}</div>
                </div>
            `;
            timeline.appendChild(endItem);
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'reviewing': '审核中',
                'approved': '审核通过',
                'rejected': '审核驳回',
                'normal': '正常'
            };
            return statusMap[status] || status;
        }

        // 搜索功能
        function searchAuditRecords() {
            const merchantId = document.getElementById('merchant-id-input').value;
            const applicant = document.getElementById('applicant-input').value;
            const auditType = document.getElementById('audit-type-select').value;
            const auditStatus = document.getElementById('audit-status-select').value;
            
            console.log('搜索条件:', { merchantId, applicant, auditType, auditStatus });
            // 这里可以实现实际的搜索逻辑
            alert('搜索功能已触发');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('merchant-id-input').value = '';
            document.getElementById('applicant-input').value = '';
            document.getElementById('audit-type-select').value = '';
            document.getElementById('audit-status-select').value = '';
        }

        // 点击遮罩关闭弹窗
        document.getElementById('detail-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });

        document.getElementById('process-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProcessModal();
            }
        });
    </script>

    <!-- 配置弹窗 -->
    <div id="config-modal" class="modal">
        <div class="modal-content" style="width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">审核规则配置</h3>
                <button class="modal-close" onclick="closeConfigModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 24px;">
                    <!-- 团队审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">团队审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置客户经理申请的审核流程，勾选上级机构将自动包含下级机构审核：
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">省级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="team-audit-province" onchange="handleTeamAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>省级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 20px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">市级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="team-audit-city" onchange="handleTeamAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>市级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 40px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">区县</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="team-audit-county" onchange="handleTeamAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>区县机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 60px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">网点</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="team-audit-outlet" onchange="handleTeamAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>网点机构审核</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 设备审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">设备审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置设备申请的审核流程，勾选上级机构将自动包含下级机构审核：
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">省级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="equipment-audit-province" onchange="handleEquipmentAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>省级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 20px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">市级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="equipment-audit-city" onchange="handleEquipmentAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>市级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 40px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">区县</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="equipment-audit-county" onchange="handleEquipmentAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>区县机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 60px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">网点</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="equipment-audit-outlet" onchange="handleEquipmentAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>网点机构审核</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 进件审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">进件审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置进件申请的审核流程，勾选上级机构将自动包含下级机构审核：
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">省级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="onboarding-audit-province" onchange="handleOnboardingAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>省级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 20px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">市级</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="onboarding-audit-city" onchange="handleOnboardingAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>市级机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 40px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">区县</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="onboarding-audit-county" onchange="handleOnboardingAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>区县机构审核</span>
                                </label>
                            </div>
                            <div style="display: flex; align-items: center; gap: 12px; margin-left: 60px;">
                                <span style="color: var(--text-secondary); font-size: 12px; width: 40px;">网点</span>
                                <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                    <input type="checkbox" id="onboarding-audit-outlet" onchange="handleOnboardingAuditChange(this)" style="margin-right: 8px; width: 16px; height: 16px;">
                                    <span>网点机构审核</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置说明 -->
                <div style="padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid var(--primary-blue);">
                    <div style="font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">配置说明：</div>
                    <ul style="margin: 0; padding-left: 16px; color: var(--text-secondary); font-size: 13px; line-height: 1.6;">
                        <li>审核流程将按照勾选的机构层级从下往上逐级审核</li>
                        <li>规则修改后将实时生效，审核中的流程不受影响，后续新的申请将按照新的规则进行审核</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
            </div>
        </div>
    </div>

    <!-- 审核通过弹窗 -->
    <div id="approve-modal" class="modal">
        <div class="modal-content" style="width: 520px;">
            <div class="modal-header">
                <h3 class="modal-title">审核通过</h3>
                <button class="modal-close" onclick="closeApproveModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">审核意见 <span style="color: var(--error-red);">*</span></label>
                    <textarea id="approve-opinion" style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; resize: vertical;" placeholder="请输入审核意见"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeApproveModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmApprove()">确认通过</button>
            </div>
        </div>
    </div>

    <!-- 审核驳回弹窗 -->
    <div id="reject-modal" class="modal">
        <div class="modal-content" style="width: 520px;">
            <div class="modal-header">
                <h3 class="modal-title">审核驳回</h3>
                <button class="modal-close" onclick="closeRejectModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 16px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">驳回原因 <span style="color: var(--error-red);">*</span></label>
                    <textarea id="reject-reason" style="width: 100%; height: 80px; padding: 8px; border: 1px solid var(--border-color); border-radius: 6px; resize: vertical;" placeholder="请输入驳回原因"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeRejectModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmReject()">确认驳回</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化多流程标签页
        function initFlowTabs(id) {
            const tabs = document.querySelectorAll('.flow-tab-item');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    
                    // 添加当前标签活动状态
                    this.classList.add('active');
                    
                    // 根据标签类型显示对应内容
                    const tabType = this.dataset.tab;
                    if (tabType === 'current') {
                        showCurrentFlow(id);
                    } else if (tabType === 'history') {
                        showHistoryFlows(id);
                    }
                });
            });
        }

        // 显示当前流程
        function showCurrentFlow(id) {
            const data = processData[id];
            if (!data || !data.hasMultipleFlows) return;
            
            const currentFlow = data.flows[data.currentFlowIndex];
            const flowData = {
                applicant: data.applicant,
                submitTime: currentFlow.submitTime,
                customerName: data.customerName,
                customerPhone: data.customerPhone,
                steps: currentFlow.steps,
                flowName: currentFlow.flowName
            };
            
            generateProcessTimeline(flowData);
        }

        // 显示历史流程
        function showHistoryFlows(id) {
            const data = processData[id];
            if (!data || !data.hasMultipleFlows) return;
            
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 获取所有历史流程（非当前流程）
            const historyFlows = data.flows.filter((flow, index) => index !== data.currentFlowIndex);
            
            if (historyFlows.length === 0) {
                timeline.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 40px;">暂无历史流程</div>';
                return;
            }
            
            // 为每个历史流程创建折叠组件
            historyFlows.forEach((flow, index) => {
                const originalIndex = data.flows.findIndex(f => f.flowId === flow.flowId);
                createHistoryFlowItem(data, flow, originalIndex, index + 1);
            });
        }

        // 创建历史流程折叠项
        function createHistoryFlowItem(data, flow, originalIndex, displayIndex) {
            const timeline = document.getElementById('process-timeline');
            
            // 创建折叠容器
            const flowItem = document.createElement('div');
            flowItem.className = 'history-flow-item';
            flowItem.id = `history-flow-${originalIndex}`;
            
            // 获取流程状态信息
            const statusInfo = getFlowStatusInfo(flow);
            
            // 创建折叠头部
            const header = document.createElement('div');
            header.className = 'history-flow-header';
            header.onclick = () => toggleHistoryFlow(originalIndex);
            
            header.innerHTML = `
                <div class="history-flow-header-info">
                    <div class="history-flow-title">第${displayIndex}次申请</div>
                    <div class="history-flow-meta">
                        <span>提交时间：${flow.submitTime}</span>
                        <span class="history-flow-status ${statusInfo.class}">${statusInfo.text}</span>
                    </div>
                </div>
                <div class="history-flow-toggle">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 9l6 6 6-6"/>
                    </svg>
                </div>
            `;
            
            // 创建折叠内容
            const content = document.createElement('div');
            content.className = 'history-flow-content';
            content.id = `history-flow-content-${originalIndex}`;
            
            const timelineContainer = document.createElement('div');
            timelineContainer.className = 'history-flow-timeline';
            
            content.appendChild(timelineContainer);
            
            // 组装折叠项
            flowItem.appendChild(header);
            flowItem.appendChild(content);
            timeline.appendChild(flowItem);
            
            // 生成流程内容（但不显示）
            generateHistoryFlowContent(data, flow, timelineContainer);
        }
        
        // 获取流程状态信息
        function getFlowStatusInfo(flow) {
            switch(flow.status) {
                case 'rejected':
                    return { text: '已驳回', class: 'rejected' };
                case 'completed':
                    return { text: '已完成', class: 'completed' };
                case 'reviewing':
                    return { text: '审核中', class: 'reviewing' };
                default:
                    return { text: '未知状态', class: 'pending' };
            }
        }
        
        // 切换历史流程展开/收起状态
        function toggleHistoryFlow(flowIndex) {
            const header = document.querySelector(`#history-flow-${flowIndex} .history-flow-header`);
            const content = document.querySelector(`#history-flow-content-${flowIndex}`);
            
            if (!header || !content) return;
            
            const isExpanded = header.classList.contains('expanded');
            
            if (isExpanded) {
                // 收起
                header.classList.remove('expanded');
                content.classList.remove('expanded');
            } else {
                // 展开
                header.classList.add('expanded');
                content.classList.add('expanded');
            }
        }
        
        // 生成历史流程内容
        function generateHistoryFlowContent(data, flow, container) {
            const flowData = {
                applicant: data.applicant,
                submitTime: flow.submitTime,
                customerName: data.customerName,
                customerPhone: data.customerPhone,
                steps: flow.steps,
                flowName: flow.flowName
            };
            
            // 清空容器
            container.innerHTML = '';
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>发起申请</span>
                            <span class="flow-status completed">已提交</span>
                        </div>
                    </div>
                    <div class="flow-institution">${flowData.applicant}</div>
                    <div class="flow-description">申请添加客户经理：${flowData.customerName}（${flowData.customerPhone}）</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${flowData.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${flowData.applicant}
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(startItem);
            
            // 添加审核节点
            flowData.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${step.statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                container.appendChild(flowItem);
            });
            
            // 添加结束节点 - 根据流程状态决定结束节点
            const lastStep = flowData.steps[flowData.steps.length - 1];
            const lastStepRejected = lastStep && lastStep.status === 'rejected';
            const allCompleted = flowData.steps.every(step => step.status === 'completed' || step.status === 'rejected');
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            
            if (lastStepRejected) {
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
            } else if (allCompleted) {
                endStatus = 'completed';
                endText = '流程完成';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${lastStepRejected ? '由于审核驳回，流程终止' : endText}</div>
                </div>
            `;
            container.appendChild(endItem);
        }

        // 配置弹窗相关函数
        function openConfigModal() {
            document.getElementById('config-modal').style.display = 'block';
        }

        function closeConfigModal() {
            document.getElementById('config-modal').style.display = 'none';
        }

        // 团队审核层级联动处理
        function handleTeamAuditChange(checkbox) {
            const level = checkbox.id.replace('team-audit-', '');
            const levels = ['province', 'city', 'county', 'outlet'];
            const currentIndex = levels.indexOf(level);
            
            if (checkbox.checked) {
                // 勾选时，自动勾选下级机构并设为只读
                for (let i = currentIndex + 1; i < levels.length; i++) {
                    const lowerCheckbox = document.getElementById(`team-audit-${levels[i]}`);
                    const lowerLabel = lowerCheckbox.parentElement;
                    lowerCheckbox.checked = true;
                    lowerCheckbox.disabled = true;
                    updateLabelStyle(lowerLabel, true);
                }
                // 其他上级机构设为只读
                for (let i = 0; i < currentIndex; i++) {
                    const upperCheckbox = document.getElementById(`team-audit-${levels[i]}`);
                    const upperLabel = upperCheckbox.parentElement;
                    upperCheckbox.disabled = true;
                    updateLabelStyle(upperLabel, upperCheckbox.checked);
                }
            } else {
                // 取消勾选时，恢复所有机构的可勾选状态且都变为未勾选
                for (let i = 0; i < levels.length; i++) {
                    const checkbox = document.getElementById(`team-audit-${levels[i]}`);
                    const label = checkbox.parentElement;
                    checkbox.disabled = false;
                    checkbox.checked = false;
                    updateLabelStyle(label, false);
                }
            }
        }

        // 设备审核层级联动处理
        function handleEquipmentAuditChange(checkbox) {
            const level = checkbox.id.replace('equipment-audit-', '');
            const levels = ['province', 'city', 'county', 'outlet'];
            const currentIndex = levels.indexOf(level);
            
            if (checkbox.checked) {
                // 勾选时，自动勾选下级机构并设为只读
                for (let i = currentIndex + 1; i < levels.length; i++) {
                    const lowerCheckbox = document.getElementById(`equipment-audit-${levels[i]}`);
                    const lowerLabel = lowerCheckbox.parentElement;
                    lowerCheckbox.checked = true;
                    lowerCheckbox.disabled = true;
                    updateLabelStyle(lowerLabel, true);
                }
                // 其他上级机构设为只读
                for (let i = 0; i < currentIndex; i++) {
                    const upperCheckbox = document.getElementById(`equipment-audit-${levels[i]}`);
                    const upperLabel = upperCheckbox.parentElement;
                    upperCheckbox.disabled = true;
                    updateLabelStyle(upperLabel, upperCheckbox.checked);
                }
            } else {
                // 取消勾选时，恢复所有机构的可勾选状态且都变为未勾选
                for (let i = 0; i < levels.length; i++) {
                    const checkbox = document.getElementById(`equipment-audit-${levels[i]}`);
                    const label = checkbox.parentElement;
                    checkbox.disabled = false;
                    checkbox.checked = false;
                    updateLabelStyle(label, false);
                }
            }
        }

        // 进件审核层级联动处理
        function handleOnboardingAuditChange(checkbox) {
            const level = checkbox.id.replace('onboarding-audit-', '');
            const levels = ['province', 'city', 'county', 'outlet'];
            const currentIndex = levels.indexOf(level);
            
            if (checkbox.checked) {
                // 勾选时，自动勾选下级机构并设为只读
                for (let i = currentIndex + 1; i < levels.length; i++) {
                    const lowerCheckbox = document.getElementById(`onboarding-audit-${levels[i]}`);
                    const lowerLabel = lowerCheckbox.parentElement;
                    lowerCheckbox.checked = true;
                    lowerCheckbox.disabled = true;
                    updateLabelStyle(lowerLabel, true);
                }
                // 其他上级机构设为只读
                for (let i = 0; i < currentIndex; i++) {
                    const upperCheckbox = document.getElementById(`onboarding-audit-${levels[i]}`);
                    const upperLabel = upperCheckbox.parentElement;
                    upperCheckbox.disabled = true;
                    updateLabelStyle(upperLabel, upperCheckbox.checked);
                }
            } else {
                // 取消勾选时，恢复所有机构的可勾选状态且都变为未勾选
                for (let i = 0; i < levels.length; i++) {
                    const checkbox = document.getElementById(`onboarding-audit-${levels[i]}`);
                    const label = checkbox.parentElement;
                    checkbox.disabled = false;
                    checkbox.checked = false;
                    updateLabelStyle(label, false);
                }
            }
        }

        // 更新标签样式
        function updateLabelStyle(label, checked) {
            const checkbox = label.querySelector('input[type="checkbox"]');
            const span = label.querySelector('span');
            
            if (checked) {
                label.style.borderColor = 'var(--primary-blue)';
                label.style.background = 'var(--primary-blue-light)';
            } else {
                label.style.borderColor = 'var(--border-color)';
                label.style.background = 'transparent';
            }
            
            // 处理禁用状态的样式
            if (checkbox.disabled) {
                label.style.opacity = '0.6';
                label.style.cursor = 'not-allowed';
                span.style.color = 'var(--text-disabled)';
            } else {
                label.style.opacity = '1';
                label.style.cursor = 'pointer';
                span.style.color = checked ? 'var(--primary-blue)' : 'var(--text-primary)';
            }
        }

        function saveConfig() {
            // 获取团队审核配置
            const teamConfig = {
                province: document.getElementById('team-audit-province').checked,
                city: document.getElementById('team-audit-city').checked,
                county: document.getElementById('team-audit-county').checked,
                outlet: document.getElementById('team-audit-outlet').checked
            };

            // 获取设备审核配置
            const equipmentConfig = {
                province: document.getElementById('equipment-audit-province').checked,
                city: document.getElementById('equipment-audit-city').checked,
                county: document.getElementById('equipment-audit-county').checked,
                outlet: document.getElementById('equipment-audit-outlet').checked
            };

            // 获取进件审核配置
            const onboardingConfig = {
                province: document.getElementById('onboarding-audit-province').checked,
                city: document.getElementById('onboarding-audit-city').checked,
                county: document.getElementById('onboarding-audit-county').checked,
                outlet: document.getElementById('onboarding-audit-outlet').checked
            };

            console.log('审核规则配置:', { teamConfig, equipmentConfig, onboardingConfig });
            alert('审核规则配置保存成功！');
            closeConfigModal();
        }

        // 审核通过弹窗
        function showApproveModal() {
            document.getElementById('approve-modal').style.display = 'block';
        }

        function closeApproveModal() {
            document.getElementById('approve-modal').style.display = 'none';
            document.getElementById('approve-opinion').value = '';
        }

        function confirmApprove() {
            const opinion = document.getElementById('approve-opinion').value.trim();
            if (!opinion) {
                alert('请输入审核意见');
                return;
            }
            
            alert('审核通过成功！');
            closeApproveModal();
            closeDetailModal();
        }

        // 审核驳回弹窗
        function showRejectModal() {
            document.getElementById('reject-modal').style.display = 'block';
        }

        function closeRejectModal() {
            document.getElementById('reject-modal').style.display = 'none';
            document.getElementById('reject-reason').value = '';
        }

        function confirmReject() {
            const reason = document.getElementById('reject-reason').value.trim();
            if (!reason) {
                alert('请输入驳回原因');
                return;
            }
            
            alert('审核驳回成功！');
            closeRejectModal();
            closeDetailModal();
        }

        // 点击遮罩关闭弹窗
        document.getElementById('config-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeConfigModal();
            }
        });

        document.getElementById('approve-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeApproveModal();
            }
        });

        document.getElementById('reject-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRejectModal();
            }
        });
    </script>
</body>
</html> 