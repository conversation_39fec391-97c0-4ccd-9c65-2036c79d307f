<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>码牌申请详情 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .detail-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .detail-item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #059669;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }
        
        .timeline {
            position: relative;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 40px;
            padding-bottom: 24px;
        }
        
        .timeline-item:last-child {
            padding-bottom: 0;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 16px;
            top: 8px;
            bottom: -16px;
            width: 2px;
            background-color: #e5e7eb;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-dot {
            position: absolute;
            left: 8px;
            top: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #e5e7eb;
            background-color: white;
        }
        
        .timeline-dot.active {
            border-color: var(--postal-green);
            background-color: var(--postal-green);
        }
        
        .timeline-dot.current {
            border-color: #f59e0b;
            background-color: #f59e0b;
        }
        
        .action-button {
            background: var(--postal-green);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            background: var(--postal-green-dark);
        }
        
        .action-button:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">码牌申请详情</h1>
            <div class="w-10"></div>
        </div>
    </div>

    <!-- 申请状态卡片 -->
    <div class="px-4 py-4">
        <div class="detail-section p-4">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 postal-green-light rounded-full flex items-center justify-center">
                    <i class="fas fa-credit-card text-postal-green text-2xl"></i>
                </div>
                <h2 class="text-lg font-medium text-gray-900 mb-2">码牌申请</h2>
                <div class="status-badge status-pending mb-4" id="statusBadge">审核中</div>
            </div>
        </div>
    </div>

    <!-- 审核信息 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">审核信息</h3>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请编号</span>
                <span class="text-gray-900 font-medium" id="applicationId">CP202407200001</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请人</span>
                <span class="text-gray-900" id="applicantName">张三</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">市级分公司</span>
                <span class="text-gray-900" id="citycom">深圳市分公司</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">区县分公司</span>
                <span class="text-gray-900" id="xiancom">南山区分公司</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">营业所</span>
                <span class="text-gray-900" id="yys">南山营业所</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请时间</span>
                <span class="text-gray-900" id="applyTime">2024-07-20 14:30</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">更新时间</span>
                <span class="text-gray-900" id="updateTime">2024-07-21 09:15</span>
            </div>

            <div class="detail-item py-3">
                <div class="mb-2">
                    <span class="text-gray-600">驳回原因</span>
                </div>
                <p class="text-gray-900 text-sm leading-relaxed" id="rejectReason">
                    -
                </p>
            </div>
        </div>
    </div>

    <!-- 码牌申请信息 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">码牌申请信息</h3>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">商户名称</span>
                <span class="text-gray-900" id="merchantName">河北快银网络科技有限公司测试</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">商户号</span>
                <span class="text-gray-900" id="merchantId">6059254143</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">负责人手机号</span>
                <span class="text-gray-900" id="phoneNumber">15910931129</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请数量上限</span>
                <span class="text-gray-900" id="quantity">40个</span>
            </div>

            <div class="detail-item py-3">
                <div class="mb-2">
                    <span class="text-gray-600">申请原因</span>
                </div>
                <p class="text-gray-900 text-sm leading-relaxed" id="applyReason">
                    商户业务扩展，需要增加收款码牌以满足多个收银台的收款需求，提升客户支付体验。
                </p>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="px-4 pb-6">
        <button class="action-button w-full" onclick="goBack()">
            <i class="fas fa-arrow-left mr-2"></i>
            返回
        </button>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 模拟申请详情数据
        const applicationDetails = {
            1: {
                id: 'CP202407200001',
                applicant: '张三',
                merchantName: '河北快银网络科技有限公司测试',
                merchantId: '6059254143',
                phone: '15910931129',
                quantity: '5个',
                applyTime: '2024-07-20 14:30',
                updateTime: '2024-07-21 09:15',
                rejectReason: '-',
                reason: '商户业务扩展，需要增加收款码牌以满足多个收银台的收款需求，提升客户支付体验。',
                status: 'pending'
            }
        };

        // 返回按钮
        function goBack() {
            window.location.href = '移动端申请中心.html';
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case 'pending':
                    return {
                        text: '审核中',
                        class: 'status-pending'
                    };
                case 'approved':
                    return {
                        text: '已通过',
                        class: 'status-approved'
                    };
                case 'rejected':
                    return {
                        text: '已驳回',
                        class: 'status-rejected'
                    };
                default:
                    return {
                        text: '未知状态',
                        class: 'status-pending'
                    };
            }
        }

        // 加载申请详情
        function loadApplicationDetail() {
            const id = getUrlParameter('id') || '1';
            const detail = applicationDetails[id];

            if (!detail) {
                alert('申请详情不存在');
                goBack();
                return;
            }

            // 更新页面内容
            document.getElementById('applicationId').textContent = detail.id;
            document.getElementById('applicantName').textContent = detail.applicant;
            document.getElementById('applyTime').textContent = detail.applyTime;
            document.getElementById('updateTime').textContent = detail.updateTime;
            document.getElementById('rejectReason').textContent = detail.rejectReason;
            document.getElementById('merchantName').textContent = detail.merchantName;
            document.getElementById('merchantId').textContent = detail.merchantId;
            document.getElementById('phoneNumber').textContent = detail.phone;
            document.getElementById('quantity').textContent = detail.quantity;
            document.getElementById('applyReason').textContent = detail.reason;

            // 更新状态信息
            const statusInfo = getStatusInfo(detail.status);
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = statusInfo.text;
            statusBadge.className = `status-badge ${statusInfo.class}`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadApplicationDetail();
        });
    </script>
</body>
</html>
