<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 设备审核管理系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-blue-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 14px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-primary:active {
            background: var(--primary-blue-active);
            border-color: var(--primary-blue-active);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-danger {
            background: var(--error-red);
            border-color: var(--error-red);
            color: #ffffff;
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
            line-height: 1.5715;
        }

        .data-table th input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
            font-weight: 400;
            line-height: 1.5715;
        }

        .data-table td,
        .data-table td span,
        .data-table td button,
        .data-table td input {
            font-size: 14px !important;
            font-weight: 400 !important;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif !important;
        }

        .data-table td .btn-link {
            font-weight: 400 !important;
        }

        .data-table td input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 表格操作列 */
        .table-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-actions .btn-link {
            font-size: 14px;
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-approved {
            background: #dcfce7;
            color: var(--success-green);
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            overflow-y: auto;
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 520px;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 流程查看弹窗专用样式 */
        .process-modal-content {
            width: 700px;
            max-width: 95vw;
            max-height: 90vh;
            height: auto;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            margin: 5vh auto;
            overflow: hidden;
        }

        .process-modal-header {
            flex-shrink: 0;
            border-bottom: 1px solid var(--border-color-light);
        }

        .process-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            max-height: calc(90vh - 120px);
            min-height: 0;
        }

        /* 美化滚动条 */
        .process-modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .process-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 流程时间线 */
        .process-flow {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px;
        }

        .process-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 20px;
            width: 2px;
            background: var(--border-color-light);
        }

        .flow-item {
            position: relative;
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
        }

        .flow-item:last-child {
            margin-bottom: 0;
        }

        .flow-node {
            position: absolute;
            left: -28px;
            top: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--bg-container);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-node.start {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.completed {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.current {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .flow-node.pending {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-node.rejected {
            background: var(--error-red);
            border-color: var(--error-red);
        }

        .flow-node.end {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-content {
            flex: 1;
            min-width: 0;
        }

        .flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .flow-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .flow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .flow-status.current {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .flow-status.pending {
            background: #f5f5f5;
            color: var(--text-secondary);
        }

        .flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .flow-institution {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .flow-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
            word-wrap: break-word;
            word-break: break-all;
        }

        .flow-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-disabled);
            flex-wrap: wrap;
        }

        .flow-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            .process-modal-content {
                width: 95%;
                margin: 5vh auto;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar fixed left-0 top-0 h-full z-40">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核中心</div>
                <div class="nav-item" onclick="switchToPage('rate')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    费率审核
                </div>
                <div class="nav-item" onclick="switchToPage('team')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    团队审核
                </div>
                <div class="nav-item" onclick="switchToPage('equipment')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>
                    设备审核
                </div>
                <div class="nav-item" onclick="switchToPage('onboarding')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    进件审核
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核中心</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">设备审核</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 设备审核页面 -->
        <div class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">设备审核</h1>
                <p class="page-description">管理商户码牌绑定数量审核申请</p>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">商户号</label>
                            <input type="text" placeholder="请输入商户号" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>未审核</option>
                                <option>审核通过</option>
                                <option>审核驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">市级分公司</label>
                            <select class="form-select">
                                <option>全部分公司</option>
                                <option>潮州市分公司</option>
                                <option>汕头市分公司</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">设备类型</label>
                            <select class="form-select">
                                <option>全部类型</option>
                                <option>码牌</option>
                                <option>POS机</option>
                                <option>扫码枪</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; align-items: end;">
                        <div class="form-group">
                            <label class="form-label">申请时间</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                                <span style="color: var(--text-secondary);">至</span>
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-default">重置</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                批量通过
                            </button>
                        </div>
                        
                        <!-- 审核配置区域 -->
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <!-- 配置按钮 -->
                            <button class="btn btn-default" onclick="openConfigModal()">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                                配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>商户号</th>
                                <th>商户名</th>
                                <th>申请人</th>
                                <th>设备类型</th>
                                <th>当前数量</th>
                                <th>申请数量</th>
                                <th>申请时间</th>
                                <th>区县审核状态</th>
                                <th>市级审核状态</th>
                                <th>省级审核状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201001</td>
                                <td>潮州市邮政便民服务点</td>
                                <td>张三</td>
                                <td>码牌</td>
                                <td>20</td>
                                <td>30</td>
                                <td>2024-12-15 09:30</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ001')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201002</td>
                                <td>汕头市邮政营业厅</td>
                                <td>王五</td>
                                <td>POS机</td>
                                <td>15</td>
                                <td>25</td>
                                <td>2024-12-14 14:20</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ002')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201003</td>
                                <td>梅州市邮政代办点</td>
                                <td>赵六</td>
                                <td>扫码枪</td>
                                <td>10</td>
                                <td>20</td>
                                <td>2024-12-13 16:45</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核驳回</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ003')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201004</td>
                                <td>揭阳市邮政支局</td>
                                <td>孙八</td>
                                <td>码牌</td>
                                <td>25</td>
                                <td>50</td>
                                <td>2024-12-12 11:30</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ004')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201005</td>
                                <td>清远市邮政便民点</td>
                                <td>周九</td>
                                <td>POS机</td>
                                <td>18</td>
                                <td>30</td>
                                <td>2024-12-11 08:15</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('EQ005')">查看流程</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 95 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">20条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">2</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">3</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                            前往 
                            <input type="number" value="1" style="width: 60px; padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; text-align: center;" min="1"> 
                            页
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 审核流程说明提示框 -->
            <div class="card" style="margin-top: 16px;">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <svg style="width: 16px; height: 16px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        <h3 class="card-title" style="color: var(--primary-blue);">设备审核流程说明</h3>
                    </div>
                </div>
                <div class="card-body" style="background: #f8faff; border-radius: 0 0 8px 8px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                        <!-- 申请条件说明 -->
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--primary-blue);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                    <line x1="8" y1="21" x2="16" y2="21"/>
                                    <line x1="12" y1="17" x2="12" y2="21"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">申请条件</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">触发条件：</span>
                                    商户设备绑定数量超过20个
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">申请方式：</span>
                                    系统自动生成申请单
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">申请内容：</span>
                                    申请增加设备绑定数量
                                </div>
                                <div>
                                    <span style="color: var(--primary-blue); font-weight: 500;">审核标准：</span>
                                    商户经营状况和设备使用情况
                                </div>
                            </div>
                        </div>
                        
                        <!-- 审核流程说明 */
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--success-green);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--success-green);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"/>
                                    <path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">审核流程</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">1. 区县级审核：</span>
                                    初步审核商户资质和申请理由
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">2. 市级审核：</span>
                                    评估商户经营状况和设备需求
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">3. 省级审核：</span>
                                    最终审核和设备数量核定
                                </div>
                                <div>
                                    <span style="color: var(--success-green); font-weight: 500;">4. 审核完成：</span>
                                    系统自动调整设备绑定限制
                                </div>
                            </div>
                        </div>
                        
                        <!-- 审核要点说明 */
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--warning-orange);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--warning-orange);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">审核要点</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">商户资质：</span>
                                    营业执照、经营许可等证件
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">经营状况：</span>
                                    交易流水、经营规模评估
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">设备使用：</span>
                                    现有设备使用率和必要性
                                </div>
                                <div>
                                    <span style="color: var(--warning-orange); font-weight: 500;">风险评估：</span>
                                    商户信用记录和风险等级
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 注意事项 -->
                    <div style="margin-top: 20px; padding: 16px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 6px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <svg style="width: 16px; height: 16px; color: var(--warning-orange); margin-top: 2px; flex-shrink: 0;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                            </svg>
                            <div>
                                <div style="font-weight: 500; color: var(--warning-orange); margin-bottom: 4px;">注意事项</div>
                                <div style="color: #8c6e1f; font-size: 13px; line-height: 1.5;">
                                    • 审核通过后，设备绑定数量限制将自动调整<br>
                                    • 驳回的申请需要商户重新提交相关证明材料<br>
                                    • 设备使用情况将纳入后续审核的重要考量因素<br>
                                    • 审核时效：区县级3个工作日，市级5个工作日，省级7个工作日
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看流程弹窗 -->
    <div id="process-modal" class="modal">
        <div class="modal-content process-modal-content">
            <div class="modal-header process-modal-header">
                <h3 class="modal-title">查看流程</h3>
                <button class="modal-close" onclick="closeProcessModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body process-modal-body">
                <!-- 审核流程时间线 -->
                <div class="process-flow" id="process-timeline">
                    <!-- 动态生成的流程内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换函数
        function switchToPage(page) {
            const pages = {
                'rate': '账户补贴审核管理系统.html',
                'team': '客户经理审核管理系统.html',
                'equipment': '设备审核管理系统.html',
                'onboarding': '进件审核管理系统.html'
            };
            
            if (pages[page]) {
                window.location.href = pages[page];
            }
        }

        // 设备审核流程数据
        const equipmentProcessData = {
            'EQ001': {
                orderId: 'EQ001',
                merchantName: '潮州市邮政便民服务点',
                applicant: '张三',
                equipmentType: '码牌',
                currentCount: 20,
                requestCount: 30,
                submitTime: '2024-12-15 09:30',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '潮州市湘桥区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户经营状况良好，设备使用合理，同意增加码牌数量。',
                        time: '2024-12-15 10:15',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '潮州市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户交易流水稳定，确实需要增加设备，审核通过。',
                        time: '2024-12-15 14:30',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过，核定码牌数量为30个。',
                        time: '2024-12-15 16:45',
                        operator: '省级审核员'
                    }
                ]
            },
            'EQ002': {
                orderId: 'EQ002',
                merchantName: '汕头市邮政营业厅',
                applicant: '王五',
                equipmentType: 'POS机',
                currentCount: 15,
                requestCount: 25,
                submitTime: '2024-12-14 14:20',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '汕头市龙湖区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户POS机使用率高，需求合理，同意申请。',
                        time: '2024-12-14 16:10',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '汕头市邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '未审核',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'EQ003': {
                orderId: 'EQ003',
                merchantName: '梅州市邮政代办点',
                applicant: '赵六',
                equipmentType: '扫码枪',
                currentCount: 10,
                requestCount: 20,
                submitTime: '2024-12-13 16:45',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '梅州市梅江区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户扫码枪使用频繁，申请数量合理。',
                        time: '2024-12-13 18:20',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '梅州市邮政分公司',
                        status: 'rejected',
                        statusText: '审核驳回',
                        content: '意见：商户近期交易量未达到申请标准，建议3个月后重新申请。',
                        time: '2024-12-14 10:30',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '未审核',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'EQ004': {
                orderId: 'EQ004',
                merchantName: '揭阳市邮政支局',
                applicant: '孙八',
                equipmentType: '码牌',
                currentCount: 25,
                requestCount: 50,
                submitTime: '2024-12-12 11:30',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '揭阳市榕城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户规模较大，码牌需求量大，初步审核通过。',
                        time: '2024-12-12 15:45',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '揭阳市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户交易数据良好，支持设备扩容申请。',
                        time: '2024-12-12 17:20',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'current',
                        statusText: '审核中',
                        content: '意见：--',
                        time: null,
                        operator: null
                    }
                ]
            },
            'EQ005': {
                orderId: 'EQ005',
                merchantName: '清远市邮政便民点',
                applicant: '周九',
                equipmentType: 'POS机',
                currentCount: 18,
                requestCount: 30,
                submitTime: '2024-12-11 08:15',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '清远市清城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户POS机使用效率高，申请理由充分。',
                        time: '2024-12-11 10:30',
                        operator: '区县审核员'
                    },
                    {
                        level: '市级审核',
                        institution: '清远市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：商户经营稳定，设备需求合理，审核通过。',
                        time: '2024-12-11 14:15',
                        operator: '市级审核员'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        content: '意见：最终审核通过，核定POS机数量为30台。',
                        time: '2024-12-11 16:45',
                        operator: '省级审核员'
                    }
                ]
            }
        };

        // 显示流程查看弹窗
        function showProcessModal(equipmentId) {
            const modal = document.getElementById('process-modal');
            const data = equipmentProcessData[equipmentId];
            
            if (!data) {
                alert('未找到该设备申请的流程信息');
                return;
            }
            
            // 生成流程
            generateEquipmentProcessFlow(data);
            
            // 显示弹窗
            modal.style.display = 'block';
            
            // 点击遮罩关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProcessModal();
                }
            });
        }

        // 关闭流程查看弹窗
        function closeProcessModal() {
            const modal = document.getElementById('process-modal');
            modal.style.display = 'none';
        }

        // 生成设备审核流程HTML
        function generateEquipmentProcessFlow(data) {
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>发起申请</span>
                            <span class="flow-status completed">已提交</span>
                        </div>
                    </div>
                    <div class="flow-institution">${data.merchantName}</div>
                    <div class="flow-description">申请增加${data.equipmentType}数量：从${data.currentCount}个增加到${data.requestCount}个</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            ${data.submitTime} 提交
                        </div>
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${data.applicant}
                        </div>
                    </div>
                </div>
            `;
            timeline.appendChild(startItem);
            
            // 添加审核节点
            data.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                let statusText = step.statusText;
                if (step.status === 'current') {
                    statusText = '审核中';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} 审核
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                timeline.appendChild(flowItem);
            });
            
            // 添加结束节点
            const allCompleted = data.steps.every(step => step.status === 'completed');
            const hasRejected = data.steps.some(step => step.status === 'rejected');
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '流程结束';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            
            if (allCompleted) {
                endStatus = 'completed';
                endText = '审核完成';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
            } else if (hasRejected) {
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                    </div>
                    <div class="flow-description">${endText}</div>
                </div>
            `;
            timeline.appendChild(endItem);
        }

        function openConfigModal() {
            document.getElementById('config-modal').style.display = 'block';
        }

        function closeConfigModal() {
            document.getElementById('config-modal').style.display = 'none';
        }

        function saveConfig() {
            // 获取选中的审核级别
            const selectedLevels = [];
            document.querySelectorAll('#config-modal input[type="checkbox"]:checked').forEach(checkbox => {
                const label = checkbox.closest('label').querySelector('span').textContent;
                selectedLevels.push(label);
            });
            
            // 显示成功提示
            alert('设备审核规则配置已保存！');
            
            // 关闭弹窗
            closeConfigModal();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // 强制设置当前页面的菜单为激活状态
            const currentPage = 'equipment'; 
            const navItems = document.querySelectorAll('.nav-section .nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('onclick') === `switchToPage('${currentPage}')`) {
                    item.classList.add('active');
                }
            });


        });
    </script>

    <!-- 审核规则配置弹窗 -->
    <div id="config-modal" class="modal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">设备审核规则配置</h3>
                <button class="modal-close" onclick="closeConfigModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 24px;">
                    
                    


                    <!-- 设备审核规则配置 -->
                    <div style="padding: 16px; border: 1px solid var(--border-color-light); border-radius: 8px;">
                        <div style="display: flex; align-items: center; margin: 0 0 16px 0;">
                            <svg style="width: 16px; height: 16px; margin-right: 8px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/>
                            </svg>
                            <h4 style="color: var(--text-primary); font-size: 16px; font-weight: 500; margin: 0;">设备审核规则配置</h4>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 13px; margin-bottom: 12px;">
                            配置设备申请的审核流程，可选择需要审核的机构：
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-province" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>省级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-city" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>市级机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; font-size: 14px;" class="checkbox-label">
                                <input type="checkbox" id="audit-county" style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>区县机构审核</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--primary-blue); border-radius: 6px; cursor: pointer; font-size: 14px; background: var(--primary-blue-light);" class="checkbox-label">
                                <input type="checkbox" id="audit-outlet" checked style="margin-right: 8px; width: 16px; height: 16px;">
                                <span>网点机构审核</span>
                            </label>
                        </div>
                        
                    </div>
                </div>

                <!-- 配置说明 -->
                <div style="padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid var(--primary-blue);">
                    <div style="font-weight: 500; color: var(--text-primary); margin-bottom: 4px;">配置说明：</div>
                    <ul style="margin: 0; padding-left: 16px; color: var(--text-secondary); font-size: 13px; line-height: 1.6;">
                        <li>审核流程将按照勾选的机构层级从下往上逐级审核</li>
                        <li>规则修改后将实时生效，审核中的流程不受影响，后续新的申请将按照新的规则进行审核</li>
                        
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
            </div>
        </div>
    </div>
</body>
</html> 