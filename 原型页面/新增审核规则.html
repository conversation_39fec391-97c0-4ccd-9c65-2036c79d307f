<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 新增审核规则</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-blue-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 14px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-label .required {
            color: var(--error-red);
            margin-left: 4px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-textarea {
            height: 80px;
            resize: vertical;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover,
        .form-textarea:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        /* 表单操作栏 */
        .form-actions {
            position: sticky;
            bottom: 0;
            background: var(--bg-container);
            border-top: 1px solid var(--border-color-light);
            padding: 16px 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0 -24px -24px;
            border-radius: 0 0 8px 8px;
        }

        /* 分组样式 */
        .form-section {
            margin-bottom: 32px;
        }

        .form-section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color-light);
        }

        /* 输入提示 */
        .form-hint {
            color: var(--text-secondary);
            font-size: 12px;
            margin-top: 4px;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            width: 44px;
            height: 22px;
            background: #ccc;
            border-radius: 11px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .switch.active {
            background: var(--primary-blue);
        }

        .switch-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        .switch.active .switch-handle {
            transform: translateX(22px);
        }
    </style>
</head>

<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">商户管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    商户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/></svg>
                    费率修改记录
                </div>
                <div class="nav-item" onclick="window.location.href='账户补贴审核管理系统.html'">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    政策审核
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 1v6m0 0 4-4m-4 4L8 3"/><path d="M12 23v-6m0 0 4 4m-4-4-4 4"/></svg>
                    分店管理
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核配置</div>
                <div class="nav-item active">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                    审核规则配置
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核配置</span>
                <span class="breadcrumb-separator">/</span>
                <span onclick="window.location.href='审核规则配置.html'" style="cursor: pointer;">审核规则配置</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">新增规则</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <div class="user-avatar">灵</div>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 页面内容 -->
        <div class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">新增审核规则</h1>
                <p class="page-description">配置新的审核规则，包括金额限制和审批层级设置</p>
            </div>

            <!-- 表单区域 -->
            <div class="card">
                <form id="ruleForm">
                    <div class="card-body">
                        <!-- 基本信息 -->
                        <div class="form-section">
                            <div class="form-section-title">基本信息</div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">机构名称<span class="required">*</span></label>
                                    <select class="form-select" required>
                                        <option value="">请选择机构</option>
                                        <option value="province">省分行</option>
                                        <option value="chaozhou">潮州市分行</option>
                                        <option value="shantou">汕头市分行</option>
                                        <option value="meizhou">梅州市分行</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">审核类型<span class="required">*</span></label>
                                    <select class="form-select" required>
                                        <option value="">请选择类型</option>
                                        <option value="subsidy">账户补贴</option>
                                        <option value="rate">费率调整</option>
                                        <option value="limit">额度审批</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">规则名称<span class="required">*</span></label>
                                    <input type="text" class="form-input" placeholder="请输入规则名称" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">规则描述</label>
                                    <textarea class="form-textarea" placeholder="请输入规则描述"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 金额限制 -->
                        <div class="form-section">
                            <div class="form-section-title">金额限制</div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">单笔上限金额<span class="required">*</span></label>
                                    <input type="number" class="form-input" placeholder="请输入金额（万元）" required>
                                    <div class="form-hint">单位：万元</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">月累计上限金额<span class="required">*</span></label>
                                    <input type="number" class="form-input" placeholder="请输入金额（万元）" required>
                                    <div class="form-hint">单位：万元</div>
                                </div>
                            </div>
                        </div>

                        <!-- 审批层级 -->
                        <div class="form-section">
                            <div class="form-section-title">审批层级配置</div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">网点审批上限<span class="required">*</span></label>
                                    <input type="number" class="form-input" placeholder="请输入金额（万元）" required>
                                    <div class="form-hint">网点级别可审批的最高金额</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">区县审批上限<span class="required">*</span></label>
                                    <input type="number" class="form-input" placeholder="请输入金额（万元）" required>
                                    <div class="form-hint">区县级别可审批的最高金额</div>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">市级审批上限<span class="required">*</span></label>
                                    <input type="number" class="form-input" placeholder="请输入金额（万元）" required>
                                    <div class="form-hint">市级可审批的最高金额</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">省级审批</label>
                                    <input type="text" class="form-input" value="超出市级上限的金额" readonly>
                                    <div class="form-hint">省级审批处理超出市级上限的所有申请</div>
                                </div>
                            </div>
                        </div>

                        <!-- 高级配置 -->
                        <div class="form-section">
                            <div class="form-section-title">高级配置</div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">省级拒绝权限</label>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-top: 8px;">
                                        <div class="switch" onclick="toggleSwitch(this)">
                                            <div class="switch-handle"></div>
                                        </div>
                                        <span>省级具有拒绝任何层级申请的权限</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">启用状态</label>
                                    <div style="display: flex; align-items: center; gap: 12px; margin-top: 8px;">
                                        <div class="switch active" onclick="toggleSwitch(this)">
                                            <div class="switch-handle"></div>
                                        </div>
                                        <span>规则启用后立即生效</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作栏 -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/><polyline points="17,21 17,13 7,13 7,21"/><polyline points="7,3 7,8 15,8"/>
                            </svg>
                            保存规则
                        </button>
                        <button type="button" class="btn btn-default" onclick="window.location.href='审核规则配置.html'">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 11H1l6-6"/><path d="m1 5 6 6"/><path d="M20 13v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h3"/>
                            </svg>
                            返回列表
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 开关切换功能
        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        // 表单提交处理
        document.getElementById('ruleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 这里可以添加表单验证逻辑
            console.log('表单提交');
            
            // 模拟保存成功
            alert('审核规则保存成功！');
            window.location.href = '审核规则配置.html';
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('新增审核规则页面已加载');
        });
    </script>
</body>
</html> 