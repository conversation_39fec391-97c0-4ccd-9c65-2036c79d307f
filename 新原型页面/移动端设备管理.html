<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .merchant-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .merchant-card:active {
            transform: scale(0.98);
            background-color: #f8f9fa;
        }
        
        .merchant-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            transition: left 0.5s;
        }
        
        .merchant-card:hover::before {
            left: 100%;
        }
        
        .search-input {
            border-radius: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-input:focus {
            box-shadow: 0 4px 12px rgba(0, 166, 81, 0.2);
        }
        
        .add-button {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--postal-green);
            transition: all 0.3s ease;
        }
        
        .add-button:hover {
            background-color: var(--postal-green-dark);
            transform: scale(1.1);
        }
        
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            animation: modalSlideUp 0.3s ease-out;
        }
        
        @keyframes modalSlideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .phone-number {
            color: #666;
            font-size: 14px;
        }
        
        .merchant-id {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3 relative">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">设备管理</h1>
            <div class="flex items-center space-x-3">
                <button class="p-2">
                    <i class="fas fa-ellipsis-h text-xl"></i>
                </button>
                <button class="p-2">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <button class="p-2" onclick="goToMerchantApply()">
                    <i class="fas fa-plus text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="mt-4 relative">
            <input 
                type="text" 
                placeholder="搜索：商户名称/手机号/登录账号" 
                class="w-full py-3 px-4 pr-12 search-input text-gray-700 placeholder-gray-500 border-none outline-none"
                id="searchInput"
            >
            <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <i class="fas fa-qrcode text-xl"></i>
            </button>
        </div>
    </div>

    <!-- 商户列表 -->
    <div class="px-4 py-4 space-y-3">
        <!-- 商户卡片 1 -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">河北快银网络科技有限公司测试</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">15910931129</span>
                        <span class="merchant-id">6059254143</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 商户卡片 2 -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">油油果超市</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">15910931129</span>
                        <span class="merchant-id">6092555417</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 商户卡片 3 -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">小而美美宜家便利店</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">18435141411</span>
                        <span class="merchant-id">6073097915</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 商户卡片 4 -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">无锡云恒商贸有限公司</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">13522169621</span>
                        <span class="merchant-id">6060838220</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 商户卡片 5 -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">个人0715003</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">13522169621</span>
                        <span class="merchant-id">6084068275</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 更多商户卡片... -->
        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">无锡云恒商贸有限公司</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">13522169621</span>
                        <span class="merchant-id">6047960944</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">无锡云恒商贸有限公司</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">13522169621</span>
                        <span class="merchant-id">6047960944</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="merchant-card bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="font-medium text-gray-900 text-base mb-1">个体对公0715002</h3>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="phone-number">13522169621</span>
                        <span class="merchant-id">6011135072</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button 
                        class="add-button flex items-center justify-center text-white"
                        onclick="showLimitModal()"
                    >
                        <i class="fas fa-plus text-sm"></i>
                    </button>
                    <button class="text-gray-400">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 限制提示弹窗 -->
    <div id="limitModal" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay absolute inset-0" onclick="closeLimitModal()"></div>
        <div class="absolute bottom-0 left-0 right-0">
            <div class="modal-content bg-white rounded-t-2xl p-6">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">码牌数量已达上限</h3>
                    <p class="text-gray-600 mb-6">该商户码牌数量已达上限，如需更多码牌，请按照流程申请。</p>
                    <div class="flex space-x-3">
                        <button 
                            onclick="closeLimitModal()" 
                            class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium"
                        >
                            取消
                        </button>
                        <button 
                            onclick="goToApplyPage()" 
                            class="flex-1 py-3 px-4 postal-green text-white rounded-lg font-medium"
                        >
                            去申请
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 返回按钮
        function goBack() {
            history.back();
        }

        // 显示限制弹窗
        function showLimitModal() {
            document.getElementById('limitModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭限制弹窗
        function closeLimitModal() {
            document.getElementById('limitModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 跳转到申请页面
        function goToApplyPage() {
            window.location.href = '移动端码牌申请.html';
        }

        // 跳转到进件申请页面
        function goToMerchantApply() {
            window.location.href = '移动端进件申请.html';
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const merchantCards = document.querySelectorAll('.merchant-card');
            
            merchantCards.forEach(card => {
                const merchantName = card.querySelector('h3').textContent.toLowerCase();
                const phoneNumber = card.querySelector('.phone-number').textContent.toLowerCase();
                const merchantId = card.querySelector('.merchant-id').textContent.toLowerCase();
                
                if (merchantName.includes(searchTerm) || 
                    phoneNumber.includes(searchTerm) || 
                    merchantId.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 防止弹窗内容区域点击时关闭弹窗
        document.querySelector('.modal-content').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html> 