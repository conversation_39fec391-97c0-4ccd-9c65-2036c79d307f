<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进件提交申请页 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .form-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .form-item:last-child {
            border-bottom: none;
        }
        
        .form-input {
            background: transparent;
            border: none;
            outline: none;
            width: 100%;
            font-size: 16px;
            color: #333;
        }
        
        .form-input::placeholder {
            color: #999;
        }
        
        .form-label {
            color: #333;
            font-weight: 500;
            min-width: 100px;
        }
        
        .section-title {
            color: #333;
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 16px;
        }
        
        .submit-button {
            background: var(--postal-green);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 16px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .submit-button:hover {
            background: var(--postal-green-dark);
        }
        
        .submit-button:active {
            transform: scale(0.98);
        }
        
        .success-modal {
            animation: modalSlideUp 0.3s ease-out;
        }
        
        @keyframes modalSlideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">进件提交申请页</h1>
            <div class="w-10"></div>
        </div>
    </div>

    <!-- 表单内容 -->
    <div class="px-4 py-6 space-y-6">
        <!-- 商户信息部分 -->
        <div class="form-section p-4">
            <h2 class="section-title">商户信息</h2>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">商户名称</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="请输入商户名称"
                    id="merchantName"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">商户负责人</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="请输入负责人姓名"
                    id="merchantOwner"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">负责人手机号</label>
                <input 
                    type="tel" 
                    class="form-input text-right" 
                    placeholder="请输入手机号"
                    id="ownerPhone"
                    pattern="[0-9]{11}"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">负责人身份证号</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="请输入身份证号"
                    id="ownerIdCard"
                    pattern="[0-9X]{18}"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">营业执照号</label>
                <input 
                    type="text" 
                    class="form-input text-right" 
                    placeholder="请输入营业执照号"
                    id="businessLicense"
                    required
                >
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">市级分公司</label>
                <select class="form-input text-right" id="cityBranch" required>
                    <option value="">请选择市级分公司</option>
                    <option value="北京市分公司">北京市分公司</option>
                    <option value="上海市分公司">上海市分公司</option>
                    <option value="广州市分公司">广州市分公司</option>
                    <option value="深圳市分公司">深圳市分公司</option>
                    <option value="杭州市分公司">杭州市分公司</option>
                    <option value="南京市分公司">南京市分公司</option>
                    <option value="成都市分公司">成都市分公司</option>
                    <option value="武汉市分公司">武汉市分公司</option>
                </select>
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">区县分公司</label>
                <select class="form-input text-right" id="countyBranch" required>
                    <option value="">请选择区县分公司</option>
                    <option value="朝阳区分公司">朝阳区分公司</option>
                    <option value="海淀区分公司">海淀区分公司</option>
                    <option value="西城区分公司">西城区分公司</option>
                    <option value="东城区分公司">东城区分公司</option>
                    <option value="丰台区分公司">丰台区分公司</option>
                    <option value="石景山区分公司">石景山区分公司</option>
                </select>
            </div>
            
            <div class="form-item py-4 flex items-center">
                <label class="form-label">营业所</label>
                <select class="form-input text-right" id="businessOffice" required>
                    <option value="">请选择营业所</option>
                    <option value="三里屯营业所">三里屯营业所</option>
                    <option value="国贸营业所">国贸营业所</option>
                    <option value="望京营业所">望京营业所</option>
                    <option value="CBD营业所">CBD营业所</option>
                    <option value="中关村营业所">中关村营业所</option>
                    <option value="西单营业所">西单营业所</option>
                </select>
            </div>
        </div>

        <!-- 申请原因部分 -->
        <div class="form-section p-4">
            <h2 class="section-title">申请原因</h2>
            
            <div class="form-item py-4">
                <textarea 
                    class="form-input resize-none" 
                    placeholder="请详细说明进件申请的原因和必要性"
                    id="applyReason"
                    rows="4"
                    required
                ></textarea>
            </div>
        </div>
    </div>

    <!-- 底部提交按钮 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <button class="submit-button" onclick="submitApplication()">
            <i class="fas fa-paper-plane mr-2"></i>
            提交申请
        </button>
    </div>

    <!-- 成功提示弹窗 -->
    <div id="successModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" onclick="closeSuccessModal()"></div>
        <div class="absolute bottom-0 left-0 right-0">
            <div class="success-modal bg-white rounded-t-2xl p-6">
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">申请提交成功</h3>
                    <p class="text-gray-600 mb-6">您的进件申请已提交，我们将在3-5个工作日内完成审核。</p>
                    <div class="flex space-x-3">
                        <button 
                            onclick="goToDeviceManagement()" 
                            class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium"
                        >
                            返回首页
                        </button>
                        <button 
                            onclick="continueApply()" 
                            class="flex-1 py-3 px-4 postal-green text-white rounded-lg font-medium"
                        >
                            继续申请
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 返回按钮
        function goBack() {
            history.back();
        }

        // 提交申请
        function submitApplication() {
            // 获取表单数据
            const formData = {
                merchantName: document.getElementById('merchantName').value,
                merchantOwner: document.getElementById('merchantOwner').value,
                ownerPhone: document.getElementById('ownerPhone').value,
                ownerIdCard: document.getElementById('ownerIdCard').value,
                businessLicense: document.getElementById('businessLicense').value,
                cityBranch: document.getElementById('cityBranch').value,
                countyBranch: document.getElementById('countyBranch').value,
                businessOffice: document.getElementById('businessOffice').value,
                applyReason: document.getElementById('applyReason').value
            };

            // 验证必填字段
            const requiredFields = [
                { field: 'merchantName', name: '商户名称' },
                { field: 'merchantOwner', name: '商户负责人' },
                { field: 'ownerPhone', name: '负责人手机号' },
                { field: 'ownerIdCard', name: '负责人身份证号' },
                { field: 'businessLicense', name: '营业执照号' },
                { field: 'cityBranch', name: '市级分公司' },
                { field: 'countyBranch', name: '区县分公司' },
                { field: 'businessOffice', name: '营业所' },
                { field: 'applyReason', name: '申请原因' }
            ];

            for (let item of requiredFields) {
                if (!formData[item.field] || formData[item.field].trim() === '') {
                    alert(`请填写${item.name}`);
                    document.getElementById(item.field).focus();
                    return;
                }
            }

            // 验证手机号格式
            const phonePattern = /^1[3-9]\d{9}$/;
            if (!phonePattern.test(formData.ownerPhone)) {
                alert('请输入正确的手机号码');
                document.getElementById('ownerPhone').focus();
                return;
            }

            // 验证身份证号格式
            const idCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idCardPattern.test(formData.ownerIdCard)) {
                alert('请输入正确的身份证号码');
                document.getElementById('ownerIdCard').focus();
                return;
            }

            // 验证营业执照号格式（统一社会信用代码）
            const licensePattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            if (!licensePattern.test(formData.businessLicense)) {
                alert('请输入正确的营业执照号（统一社会信用代码）');
                document.getElementById('businessLicense').focus();
                return;
            }

            // 验证申请原因长度
            if (formData.applyReason.length < 10) {
                alert('申请原因不能少于10个字符');
                document.getElementById('applyReason').focus();
                return;
            }

            // 模拟提交
            console.log('提交进件申请数据:', formData);
            
            // 显示成功弹窗
            showSuccessModal();
        }

        // 显示成功弹窗
        function showSuccessModal() {
            document.getElementById('successModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭成功弹窗
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 返回设备管理页面
        function goToDeviceManagement() {
            window.location.href = '移动端设备管理.html';
        }

        // 继续申请
        function continueApply() {
            closeSuccessModal();
            // 清空表单
            document.querySelectorAll('input, select, textarea').forEach(element => {
                element.value = '';
            });
            // 滚动到顶部
            window.scrollTo(0, 0);
        }

        // 防止弹窗内容区域点击时关闭弹窗
        document.querySelector('.success-modal').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 页面加载时添加底部间距，避免被固定按钮遮挡
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.paddingBottom = '100px';
        });

        // 手机号输入格式化
        document.getElementById('ownerPhone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 身份证号输入格式化
        document.getElementById('ownerIdCard').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9X]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value.toUpperCase();
        });

        // 营业执照号输入格式化
        document.getElementById('businessLicense').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9A-HJ-NPQRTUWXY]/g, '');
            if (value.length > 18) {
                value = value.slice(0, 18);
            }
            e.target.value = value.toUpperCase();
        });
    </script>
</body>
</html> 