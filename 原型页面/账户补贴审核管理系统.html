<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 账户补贴审核管理系统</title>
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --warning-grey: #8c8c8c;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-blue-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 14px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn1 {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-primary:active {
            background: var(--primary-blue-active);
            border-color: var(--primary-blue-active);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-danger {
            background: var(--error-red);
            border-color: var(--error-red);
            color: #ffffff;
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
            line-height: 1.5715;
        }

        .data-table th input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
            font-weight: 400;
            line-height: 1.5715;
        }

        /* 确保表格内所有文本统一样式 */
        .data-table td,
        .data-table td span,
        .data-table td button,
        .data-table td input {
            font-size: 14px !important;
            font-weight: 400 !important;
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif !important;
        }

        /* 特殊内容的字重调整 */
        .data-table td .btn-link {
            font-weight: 400 !important;
        }

        .data-table td input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 表格操作列 */
        .table-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-actions .btn-link {
            font-size: 14px;
        }

        /* 页面标签样式 */
        .page-tabs {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
        }

        .tab-item {
            display: inline-block;
            padding: 16px 0;
            margin-right: 32px;
            color: var(--text-secondary);
            font-weight: 500;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .tab-item:hover {
            color: var(--primary-blue);
        }

        .tab-item.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-approved {
            background: #dcfce7;
            color: var(--post-green);
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.25);
            transition: 0.2s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: 0.2s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input:checked + .slider {
            background-color: var(--primary-blue);
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.45);
            overflow-y: auto; /* 允许整个弹窗区域滚动 */
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            padding: 0;
            border-radius: 8px;
            width: 520px;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .form-item {
            margin-bottom: 24px;
        }

        .form-item:last-child {
            margin-bottom: 0;
        }

        .form-item-label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 400;
            color: var(--text-primary);
        }

        .form-item-required {
            color: var(--error-red);
            margin-left: 4px;
        }

        .form-item-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .input-with-suffix {
            position: relative;
            display: inline-block;
            flex: 1;
        }

        .input-suffix {
            position: absolute;
            right: 11px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 14px;
            pointer-events: none;
        }

        .input-with-suffix .form-input {
            padding-right: 30px;
        }

        .info-tip {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
            color: #389e0d;
            margin-top: 16px;
        }

        .info-tip .tip-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        /* 提示框样式 */
        .tooltip {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: var(--text-primary);
            color: white;
            padding: 0;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.4;
            min-width: 280px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            transform: translateY(-4px);
        }

        .tooltip-title {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 14px;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            font-size: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tooltip-content {
            padding: 12px 14px;
        }

        .tooltip-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            gap: 8px;
        }

        .tooltip-item:last-child {
            margin-bottom: 0;
        }

        .tooltip-label {
            font-weight: 500;
            color: #e3f2fd;
            white-space: nowrap;
            font-size: 12px;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            bottom: 100%;
            right: 16px;
            border: 6px solid transparent;
            border-bottom-color: var(--text-primary);
        }

        #tip-icon:hover + .tooltip,
        .tooltip:hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        /* 流程查看弹窗样式 */
        .process-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .process-title {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .process-version {
            color: var(--text-disabled);
        }

        .flow-node {
            position: absolute;
            left: -28px;
            top: 2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--bg-container);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-node.start {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.completed {
            background: var(--success-green);
            border-color: var(--success-green);
        }

        .flow-node.current {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .flow-node.pending {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-node.rejected {
            background: var(--error-red);
            border-color: var(--error-red);
        }

        .flow-node.end {
            background: var(--border-color);
            border-color: var(--border-color);
        }

        .flow-content {
            flex: 1;
            min-width: 0;
        }

        .flow-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 4px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .flow-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .flow-title-right {
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 400;
        }

        .flow-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .flow-status.completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .flow-status.current {
            background: #ece9e4;
            color: #8c8c8c;
        }

        .flow-status.pending {
            background: #f5f5f5;
            color: var(--text-secondary);
        }

        .flow-status.rejected {
            background: #fff2f0;
            color: var(--error-red);
        }

        .flow-institution {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .flow-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .flow-action-btn {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        .flow-action-btn.approve {
            background: var(--primary-blue);
            color: white;
        }

        .flow-action-btn.processing {
            background: var(--warning-orange);
            color: white;
        }

        .auto-approve-tag {
            background: var(--success-green);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
        }

        /* 流程查看弹窗专用样式 */
        .process-modal-content {
            width: 700px;
            max-width: 95vw;
            max-height: 90vh;
            height: auto;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            margin: 5vh auto;
            overflow: hidden;
        }

        .process-modal-header {
            flex-shrink: 0;
            border-bottom: 1px solid var(--border-color-light);
        }

        .process-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            max-height: calc(90vh - 120px); /* 减去头部和可能的底部空间 */
            min-height: 0; /* 重要：允许flex item收缩 */
        }

        /* 美化滚动条 */
        .process-modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .process-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .process-modal-body::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 流程时间线优化 */
        .process-flow {
            position: relative;
            padding-left: 40px;
            padding-bottom: 20px; /* 添加底部间距 */
        }

        .process-flow::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 20px; /* 调整底部位置 */
            width: 2px;
            background: var(--border-color-light);
        }

        /* 流程项间距优化 */
        .flow-item {
            position: relative;
            margin-bottom: 24px; /* 增加间距 */
            display: flex;
            align-items: flex-start;
        }

        .flow-item:last-child {
            margin-bottom: 0;
        }

        /* 流程描述文本优化 */
        .flow-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 8px;
            word-wrap: break-word;
            word-break: break-all;
        }

        /* 流程元信息优化 */
        .flow-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-disabled);
            flex-wrap: wrap; /* 允许换行 */
        }

        .flow-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .main-content {
                margin-left: 0;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            .process-info-grid {
                grid-template-columns: 1fr;
            }
            
            /* 流程弹窗响应式优化 */
            .process-modal-content {
                width: 95vw;
                height: 90vh;
                margin: 5vh auto;
                max-height: 90vh;
            }
            
            .process-modal-body {
                padding: 16px;
                max-height: calc(90vh - 100px);
            }
            
            .process-flow {
                padding-left: 30px;
            }
            
            .process-flow::before {
                left: 15px;
            }
            
            .flow-node {
                left: -23px;
            }
            
            .flow-meta {
                gap: 12px;
            }
            
            .flow-meta-item {
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .process-modal-content {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                max-height: 100vh;
            }
            
            .process-modal-body {
                padding: 12px;
                max-height: calc(100vh - 80px);
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar fixed left-0 top-0 h-full z-40">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核中心</div>
                <div class="nav-item active" onclick="switchToPage('rate')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    费率审核
                </div>
                <div class="nav-item" onclick="switchToPage('team')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    团队审核
                </div>
                <div class="nav-item" onclick="switchToPage('equipment')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>
                    设备审核
                </div>
                <div class="nav-item" onclick="switchToPage('onboarding')">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    进件审核
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核配置</div>
                <div class="nav-item" onclick="showRuleConfigPage()">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                    审核规则配置
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核中心</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">费率审核</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 政策审核页面 -->
        <div id="audit-page" class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">费率审核</h1>
                 </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">商户号</label>
                            <input type="text" placeholder="请输入商户号" class="form-input">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>未审核</option>
                                <option>审核通过</option>
                                <option>审核驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">市级分公司</label>
                            <select class="form-select">
                                <option>全部分公司</option>
                                <option>潮州市分公司</option>
                                <option>汕头市分公司</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核类型</label>
                            <select class="form-select">
                                <option>全部类型</option>
                                <option>账户补贴</option>
                                <option>费率调整</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 16px; align-items: end;">
                        <div class="form-group">
                            <label class="form-label">提交时间</label>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                                <span style="color: var(--text-secondary);">至</span>
                                <input type="date" value="2025-06-04" class="form-input" style="flex: 1;">
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                                </svg>
                                搜索
                            </button>
                            <button class="btn btn-default">重置</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <div style="display: flex; gap: 8px;">
                            <button class="btn btn-primary">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                批量通过
                            </button>
                        </div>
                        
                        <!-- 免审配置区域 -->
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <!-- 免审状态显示区域 -->
                            <div id="auto-approve-status" style="display: flex; align-items: center; gap: 16px;">
                                <!-- 账户补贴免审状态 -->
                                <div id="account-subsidy-status" style="display: none; align-items: center; gap: 8px;">
                                    <span style="color: var(--text-secondary); font-size: 14px;">账户补贴免审：</span>
                                    <span style="font-size: 14px; color: var(--success-green);">已开启</span>
                                    <!-- 提示信息 -->
                                    <div style="display: flex; align-items: center; gap: 8px; position: relative;">
                                        <span id="tip-icon" style="display: inline-flex; align-items: center; justify-content: center; width: 16px; height: 16px; background: var(--primary-blue); color: white; border-radius: 50%; font-size: 10px; font-weight: bold; cursor: pointer; user-select: none;">?</span>
                                        <div id="tip-tooltip" class="tooltip">
                                            <div class="tooltip-title">账户补贴自动免审说明</div>
                                            <div class="tooltip-content">
                                                <div class="tooltip-item">
                                                    <span class="tooltip-label">判断依据：</span>
                                                    <span>商户单次申请补贴金额</span>
                                                </div>
                                                <div class="tooltip-item">
                                                    <span class="tooltip-label">处理方式：</span>
                                                    <span>账户补贴审核将基于预设金额阈值自动处理：未超过阈值免审通过，超过阈值需人工审核。</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 费率审核免审状态 -->
                                <div id="rate-audit-status" style="display: none; align-items: center; gap: 8px;">
                                    <span style="color: var(--text-secondary); font-size: 14px;">费率审核免审：</span>
                                    <span style="font-size: 14px; color: var(--success-green);">已开启</span>
                                </div>
                                
                                <!-- 补贴包免审状态 -->
                                <div id="subsidy-package-status" style="display: none; align-items: center; gap: 8px;">
                                    <span style="color: var(--text-secondary); font-size: 14px;">补贴包免审：</span>
                                    <span style="font-size: 14px; color: var(--success-green);">已开启</span>
                                </div>
                                
                                <!-- 提现费率审核免审状态 -->
                                <div id="withdraw-rate-status" style="display: none; align-items: center; gap: 8px;">
                                    <span style="color: var(--text-secondary); font-size: 14px;">提现费率审核免审：</span>
                                    <span style="font-size: 14px; color: var(--success-green);">已开启</span>
                                </div>
                            </div>
                            
                            <!-- 配置按钮 -->
                            <button class="btn btn-default" id="config-btn" onclick="openAutoApproveModal()">
                                <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                                配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>商户号</th>
                                <th>商户名</th>
                                <th>申请人</th>
                                <th>提交时间</th>
                                <th>更新时间</th>
                                <th>生效时间</th>
                                <th>区县审核状态</th>
                                <th>市级审核状态</th>
                                <th>省级审核状态</th>
                                <th>审核类型</th>
                                
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201001</td>
                                <td>潮州市邮政便民服务点</td>
                                <td>张三</td>
                                <td>2024-12-15 09:30</td>
                                <td>2024-12-15 09:30</td>
                                <td>2024-12-15 09:30</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                                
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201001')">查看流程</button>
                                        </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201002</td>
                                <td>汕头市邮政营业厅</td>
                                <td>王五</td>
                                <td>2024-12-14 14:20</td>
                                <td>-</td>
                                <td>-</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                                
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201002')">查看流程</button>
                                        </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201003</td>
                                <td>梅州市邮政代办点</td>
                                <td>赵六</td>
                                <td>2024-12-13 16:45</td>
                                <td>2024-12-14 09:15</td>
                                <td>-</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核驳回</span></td>
                               
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                              
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201003')">查看流程</button>
                                         </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201004</td>
                                <td>揭阳市邮政支局</td>
                                <td>孙八</td>
                                <td>2024-12-12 11:30</td>
                                <td>-</td>
                                <td>-</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                
                                <td><span style="background: #ece9e4; color: var(--warning-grey); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">未审核</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                                
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201004')">查看流程</button>
                                        </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201005</td>
                                <td>清远市邮政便民点</td>
                                <td>周九</td>
                                <td>2024-12-11 08:15</td>
                                <td>2024-12-11 08:15</td>
                                <td>2024-12-11 08:15</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                               
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201005')">查看流程</button>
                                        </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>20241201006</td>
                                <td>东莞市邮政代办点</td>
                                <td>陈一</td>
                                <td>2024-12-10 16:20</td>
                                <td>2024-12-11 10:30</td>
                                <td>2024-12-11 11:00</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">审核通过</span></td>
                                <td><span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span></td>
                                
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-link">详情</button>
                                        <button class="btn-link" onclick="showProcessModal('20241201006')">查看流程</button>
                                        </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 157 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">20条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">2</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">3</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                            前往 
                            <input type="number" value="1" style="width: 60px; padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; text-align: center;" min="1"> 
                            页
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 审核流程说明提示框 -->
            <div class="card" style="margin-top: 16px;">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <svg style="width: 16px; height: 16px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        <h3 class="card-title" style="color: var(--primary-blue);">审核流程说明（原型逻辑说明）</h3>
                    </div>
                </div>
                <div class="card-body" style="background: #f8faff; border-radius: 0 0 8px 8px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                        <!-- 机构审核说明 -->
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--primary-blue);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--primary-blue);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                    <circle cx="9" cy="7" r="4"/>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">机构审核流程</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">1. 点击详情审核：</span>
                                    进入详情页面查看申请信息
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">2. 审核操作：</span>
                                    点击"通过"或"驳回"按钮
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--primary-blue); font-weight: 500;">3. 填写意见：</span>
                                    弹出审核意见框，填写审核意见
                                </div>
                                <div>
                                    <span style="color: var(--primary-blue); font-weight: 500;">4. 流程推进：</span>
                                    审核完成后自动进入下一级审核
                                </div>
                            </div>
                        </div>
                        
                        <!-- 超管审核说明 -->
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--success-green);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--success-green);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                    <path d="M9 12l2 2 4-4"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">超管审核流程</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">1. 最终审核：</span>
                                    超管具有最终审核权限
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">2. 审核操作：</span>
                                    点击"审核"或"驳回"按钮
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--success-green); font-weight: 500;">3. 填写意见：</span>
                                    弹出审核意见框，填写最终意见
                                </div>
                                <div>
                                    <span style="color: var(--success-green); font-weight: 500;">4. 流程结束：</span>
                                    <span style="color: var(--success-green); font-weight: 500;">审核通过即全部审核通过，流程结束</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 免审机制说明 -->
                        <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid var(--warning-orange);">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                                <svg style="width: 18px; height: 18px; color: var(--warning-orange);" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
                                    <path d="M8 12l2 2 4-4"/>
                                    <path d="M12 1v6m0 0l4-4m-4 4L8 3"/>
                                </svg>
                                <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: var(--text-primary);">自动免审机制</h4>
                            </div>
                            <div style="color: var(--text-secondary); line-height: 1.6;">
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">阈值判断：</span>
                                    根据申请金额自动判断是否免审
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">自动通过：</span>
                                    未超过阈值的申请自动审核通过，审核意见默认为：系统自动审核通过。
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <span style="color: var(--warning-orange); font-weight: 500;">人工审核：</span>
                                    超过阈值的申请进入人工审核流程，手动填写审核意见。
                                </div>
                                <div>
                                    <span style="color: var(--warning-orange); font-weight: 500;">配置管理：</span>
                                    可通过"配置"按钮调整免审阈值
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 注意事项 -->
                    <div style="margin-top: 20px; padding: 16px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 6px;">
                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                            <svg style="width: 16px; height: 16px; color: var(--warning-orange); margin-top: 2px; flex-shrink: 0;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                            </svg>
                            <div>
                                <div style="font-weight: 500; color: var(--warning-orange); margin-bottom: 4px;">注意事项</div>
                                <div style="color: #8c6e1f; font-size: 13px; line-height: 1.5;">
                                    • 人工审核时，审核意见为必填项<br>
                                    • 驳回后的申请需要申请人重新提交<br>
                                    • 在申请提交上来后超管全程可见，并可进行审核，超管审核后即流程全部审核通过<br>
                                    • 免审配置修改后立即生效。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 审核规则配置页面 -->
        <div id="rule-config-page" class="page-container" style="display: none;">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">审核规则配置</h1>
                  </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="form-group">
                            <label class="form-label">机构名称</label>
                            <select class="form-select">
                                <option>全部机构</option>
                                <option>潮州市分公司</option>
                                <option>汕头市分公司</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">规则类型</label>
                            <select class="form-select">
                                <option>全部类型</option>
                                <option>账户补贴</option>
                                <option>添加客户经理</option>
                                
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>已启用</option>
                                <option>已停用</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                            </svg>
                            搜索
                        </button>
                        <button class="btn btn-default">重置</button>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    <div " style="display: flex; gap: 8px;">
                        <button class="btn btn-primary" onclick="window.location.href='新增审核规则.html'">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"/><path d="M12 5v14"/>
                            </svg>
                            新增规则
                        </button>
                        <button class="btn btn-default">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 16.5v2a2.5 2.5 0 0 0 2.5 2.5h13a2.5 2.5 0 0 0 2.5-2.5v-2"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>规则编号</th>
                                <th>规则名称</th>
                                <th>规则类型</th>
                                <th>机构级别</th>
                                <th>机构名称</th>
                                <th>状态</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ZH20250605001</td>
                                <td>潮州市通用补贴审核规则</td>
                                 <td>
                                    <span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span>
                                </td>
                                <td>市级</td>
                                <td>潮州市分公司</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">已启用</span></td>
                                <td>张三</td>
                                <td>2024-12-15 10:30</td>
                                <td>2024-12-15 10:30</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=1'">编辑</button>
                                        <button class="btn-link" style="color: var(--error-red);">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>ZH20250605002</td>
                                <td>汕头市通用补贴审核规则</td>
                                <td>
                                    <span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span>
                                </td>
                                <td>市级</td>
                                <td>汕头市分公司</td>
                                <td><span style="background: #f6ffed; color: var(--success-green); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">已启用</span></td>
                                <td>张三</td>
                                <td>2024-12-10 14:20</td>
                                <td>2024-12-10 14:20</td>
                                
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=2'">编辑</button>
                                        <button class="btn-link" style="color: var(--error-red);">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>ZH20250605003</td>
                                <td>佛山市添加客户经理审核规则</td>
                                <td>
                                    <span style="background: #f0f9ff; color: #0ea5e9; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">添加客户经理</span>
                                </td>
                                <td>市级</td>
                                <td>佛山市分公司</td>
                                <td><span style="background: #fff2f0; color: var(--error-red); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">已停用</span></td>
                                <td>张三</td>
                                <td>2024-11-28 09:15</td>
                                <td>2024-11-28 09:15</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=3'">编辑</button>
                                        <button class="btn-link" style="color: var(--success-green);">启用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-top: 1px solid var(--border-color-light);">
                    <div style="color: var(--text-secondary); font-size: 14px;">共 3 条记录</div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                        <span style="color: var(--text-secondary); font-size: 14px;">10条/页</span>
                        <div style="display: flex; gap: 4px;">
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">‹</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--primary-blue); background: var(--primary-blue); color: #ffffff; border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">1</button>
                            <button style="padding: 4px 8px; border: 1px solid var(--border-color); background: var(--bg-container); color: var(--text-primary); border-radius: 4px; cursor: pointer; font-size: 14px; min-width: 32px; text-align: center;">›</button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                            前往 
                            <input type="number" value="1" style="width: 60px; padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; text-align: center;" min="1"> 
                            页
                        </div>
                    </div>
                </div>
            </div>

           
                
               
            </div>
                     </div>
        </div>

    </div>

    <!-- 免审配置弹窗 -->
    <div id="auto-approve-modal" class="modal">
        <div class="modal-content" style="width: 600px;">
            <div class="modal-header">
                <h3 class="modal-title">自动免审配置</h3>
                <button class="modal-close" onclick="closeAutoApproveModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <!-- 账户补贴免审配置 -->
                <div class="form-item">
                    <div style="display: flex; align-items: center; justify-content: flex-start;  margin-bottom: 16px;">
                        <div class="form-item-label" style="margin-bottom: 0; font-weight: 500;">
                            账户补贴免审
                        </div>
                        <label class="switch" style="margin: auto 2%;">
                            <input type="checkbox" id="account-subsidy-switch" >
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <!-- 账户补贴金额阈值配置 -->
                    <div id="account-subsidy-config" style="display: none;">
                        <div class="form-item" style="margin-bottom: 16px;">
                            <div class="form-item-label">
                                账户补贴免审金额阈值
                                <span class="form-item-required">*</span>
                            </div>
                            <div class="form-item-content">
                                <div class="input-with-suffix">
                                    <input type="number" 
                                           id="approve-amount" 
                                           class="form-input" 
                                           placeholder="请输入免审金额阈值" 
                                           min="1" 
                                           max="10000" 
                                           step="0.01">
                                    <span class="input-suffix">元</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-tip" style="margin-bottom: 0;">
                            <div class="tip-title">说明：</div>
                            <div>• 免审金额是指商户单次申请补贴金额</div>
                            <div>• 未超过设定金额自动审核通过，进入下一级审核</div>
                            <div>• 超过设定金额将进入人工审核，通过后进入下一级审核</div>
                        </div>
                    </div>
                </div>

                <!-- 费率审核免审配置 -->
                <div class="form-item" style="border-top: 1px solid var(--border-color-light); padding-top: 24px;">
                    <div style="display: flex; align-items: center; justify-content: flex-start;">
                        <div class="form-item-label" style="margin-bottom: 0; font-weight: 500;">
                            费率审核免审
                        </div>
                        <label class="switch" style="margin: auto 2%;">
                            <input type="checkbox" id="rate-audit-switch">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- 补贴包免审配置 -->
                <div class="form-item" style="border-top: 1px solid var(--border-color-light); padding-top: 24px;">
                    <div style="display: flex; align-items: center; justify-content: flex-start;">
                        <div class="form-item-label" style="margin-bottom: 0; font-weight: 500;">
                            补贴包免审
                        </div>
                        <label class="switch" style="margin: auto 2%;">
                            <input type="checkbox" id="subsidy-package-switch">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- 提现费率审核免审配置 -->
                <div class="form-item" style="border-top: 1px solid var(--border-color-light); padding-top: 24px;">
                    <div style="display: flex; align-items: center; justify-content: flex-start;">
                        <div class="form-item-label" style="margin-bottom: 0; font-weight: 500;">
                            提现费率审核免审
                        </div>
                        <label class="switch" style="margin: auto 2%;">
                            <input type="checkbox" id="withdraw-rate-switch">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeAutoApproveModal()">取消</button>
                <button class="btn btn-primary" onclick="saveAutoApproveConfig()">保存</button>
            </div>
        </div>
    </div>

    <!-- 流程查看弹窗 -->
    <div id="process-modal" class="modal">
        <div class="modal-content process-modal-content">
            <div class="modal-header process-modal-header">
                <h3 class="modal-title">查看流程</h3>
                <button class="modal-close" onclick="closeProcessModal()">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body process-modal-body">
                <!-- 流程信息头部 -->
                <!-- <div class="process-header"> -->
                    <!-- <div class="process-title"> -->
                        <!-- <span>流程：</span> -->
                        <!-- <span id="process-flow-name">销售合同新增流程</span> -->
                        
                    <!-- </div> -->
                <!-- </div> -->

                <!-- 审核流程时间线 -->
                <div class="process-flow" id="process-timeline">
                    <!-- 动态生成的流程内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换函数
        function switchToPage(page) {
            const pages = {
                'rate': '账户补贴审核管理系统.html',
                'team': '客户经理审核管理系统.html',
                'equipment': '设备审核管理系统.html',
                'onboarding': '进件审核管理系统.html'
            };
            
            if (pages[page]) {
                window.location.href = pages[page];
            }
        }

        function showAuditPage() {
            // 显示政策审核页面
            document.getElementById('audit-page').style.display = 'block';
            document.getElementById('rule-config-page').style.display = 'none';
            
            // 更新侧边栏导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 设置政策审核为激活状态
            const auditNavItem = navItems[4]; // 政策审核菜单项
            if (auditNavItem) {
                auditNavItem.classList.add('active');
            }
            
            // 更新面包屑
            const breadcrumbActive = document.querySelector('.breadcrumb-active');
            if (breadcrumbActive) {
                breadcrumbActive.textContent = '政策审核';
            }
        }

        function showRuleConfigPage() {
            // 显示审核规则配置页面
            document.getElementById('audit-page').style.display = 'none';
            document.getElementById('rule-config-page').style.display = 'block';
            
            // 更新侧边栏导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 设置审核规则配置为激活状态
            const ruleConfigNavItem = navItems[6]; // 审核规则配置菜单项
            if (ruleConfigNavItem) {
                ruleConfigNavItem.classList.add('active');
            }
            
            // 更新面包屑
            const breadcrumbActive = document.querySelector('.breadcrumb-active');
            if (breadcrumbActive) {
                breadcrumbActive.textContent = '审核规则配置';
            }
        }

        // 免审配置管理
        let autoApproveConfig = {
            accountSubsidy: {
                enabled: false,
                amount: 500
            },
            rateAudit: {
                enabled: false
            },
            subsidyPackage: {
                enabled: false
            },
            withdrawRate: {
                enabled: false
            }
        };

        // 确保配置对象结构完整的辅助函数
        function ensureConfigStructure(config) {
            if (!config) {
                return {
                    accountSubsidy: { enabled: false, amount: 500 },
                    rateAudit: { enabled: false },
                    subsidyPackage: { enabled: false },
                    withdrawRate: { enabled: false }
                };
            }
            
            if (!config.accountSubsidy) {
                config.accountSubsidy = { enabled: false, amount: 500 };
            }
            if (!config.rateAudit) {
                config.rateAudit = { enabled: false };
            }
            if (!config.subsidyPackage) {
                config.subsidyPackage = { enabled: false };
            }
            if (!config.withdrawRate) {
                config.withdrawRate = { enabled: false };
            }
            
            return config;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('政策审核页面已加载');
            // 默认显示政策审核页面
            showAuditPage();
            
            // 初始化免审配置
            initAutoApproveConfig();
            
            // 绑定开关事件
            bindAutoApproveEvents();
            
            // 强制设置当前页面的菜单为激活状态
            const currentPage = 'rate'; 
            const navItems = document.querySelectorAll('.nav-section .nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('onclick') === `switchToPage('${currentPage}')`) {
                    item.classList.add('active');
                }
            });
        });

        // 初始化免审配置
        function initAutoApproveConfig() {
            try {
                // 从localStorage读取配置（模拟后端数据）
                const savedConfig = localStorage.getItem('autoApproveConfig');
                if (savedConfig) {
                    const parsedConfig = JSON.parse(savedConfig);
                    autoApproveConfig = ensureConfigStructure(parsedConfig);
                } else {
                    // 如果没有保存的配置，设置一个默认的演示配置
                    autoApproveConfig = {
                        accountSubsidy: {
                            enabled: true,
                            amount: 500
                        },
                        rateAudit: {
                            enabled: false
                        },
                        subsidyPackage: {
                            enabled: false
                        },
                        withdrawRate: {
                            enabled: false
                        }
                    };
                    // 保存默认配置
                    saveConfigToStorage();
                }
            } catch (error) {
                console.error('初始化免审配置出错:', error);
                // 出错时使用默认配置
                autoApproveConfig = ensureConfigStructure(null);
                saveConfigToStorage();
            }
            
            // 更新界面显示
            updateAutoApproveDisplay();
        }

        // 更新免审配置显示
        function updateAutoApproveDisplay() {
            try {
                // 确保配置结构完整
                autoApproveConfig = ensureConfigStructure(autoApproveConfig);
                
                // 更新各个免审功能的状态显示
                const accountSubsidyStatus = document.getElementById('account-subsidy-status');
                const rateAuditStatus = document.getElementById('rate-audit-status');
                const subsidyPackageStatus = document.getElementById('subsidy-package-status');
                const withdrawRateStatus = document.getElementById('withdraw-rate-status');
                
                // 账户补贴免审状态
                if (accountSubsidyStatus) {
                    if (autoApproveConfig.accountSubsidy && autoApproveConfig.accountSubsidy.enabled) {
                        accountSubsidyStatus.style.display = 'flex';
                    } else {
                        accountSubsidyStatus.style.display = 'none';
                    }
                }
                
                // 费率审核免审状态
                if (rateAuditStatus) {
                    if (autoApproveConfig.rateAudit && autoApproveConfig.rateAudit.enabled) {
                        rateAuditStatus.style.display = 'flex';
                    } else {
                        rateAuditStatus.style.display = 'none';
                    }
                }
                
                // 补贴包免审状态
                if (subsidyPackageStatus) {
                    if (autoApproveConfig.subsidyPackage && autoApproveConfig.subsidyPackage.enabled) {
                        subsidyPackageStatus.style.display = 'flex';
                    } else {
                        subsidyPackageStatus.style.display = 'none';
                    }
                }
                
                // 提现费率审核免审状态
                if (withdrawRateStatus) {
                    if (autoApproveConfig.withdrawRate && autoApproveConfig.withdrawRate.enabled) {
                        withdrawRateStatus.style.display = 'flex';
                    } else {
                        withdrawRateStatus.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('更新免审配置显示出错:', error);
            }
        }

        // 绑定自动免审相关事件
        function bindAutoApproveEvents() {
            // 等到弹窗打开时再绑定事件，因为初始时这些元素可能还不存在
            console.log('免审事件绑定已初始化');
        }

        // 绑定弹窗内的开关事件（在弹窗打开时调用）
        function bindModalEvents() {
            // 弹窗内的账户补贴开关事件
            const accountSubsidySwitch = document.getElementById('account-subsidy-switch');
            if (accountSubsidySwitch) {
                // 使用 onchange 属性直接绑定，避免重复绑定问题
                accountSubsidySwitch.onchange = handleAccountSubsidyChange;
            }
        }

        // 账户补贴开关变化处理函数
        function handleAccountSubsidyChange(event) {
            const configDiv = document.getElementById('account-subsidy-config');
            const isChecked = event.target.checked;
            if (isChecked) {
                configDiv.style.display = 'block';
            } else {
                configDiv.style.display = 'none';
            }
        }

        // 打开免审配置弹窗
        function openAutoApproveModal() {
            try {
                // 确保配置结构完整
                autoApproveConfig = ensureConfigStructure(autoApproveConfig);
                
                const modal = document.getElementById('auto-approve-modal');
                if (!modal) {
                    console.error('找不到免审配置弹窗元素');
                    return;
                }
                
                // 初始化弹窗数据
                const accountSubsidySwitch = document.getElementById('account-subsidy-switch');
                const rateAuditSwitch = document.getElementById('rate-audit-switch');
                const subsidyPackageSwitch = document.getElementById('subsidy-package-switch');
                const withdrawRateSwitch = document.getElementById('withdraw-rate-switch');
                const amountInput = document.getElementById('approve-amount');
                const configDiv = document.getElementById('account-subsidy-config');
                
                // 检查关键元素是否存在
                if (!accountSubsidySwitch || !rateAuditSwitch || !subsidyPackageSwitch || !withdrawRateSwitch || !amountInput || !configDiv) {
                    console.error('弹窗内的某些必要元素未找到');
                    return;
                }
                
                // 设置开关状态
                accountSubsidySwitch.checked = autoApproveConfig.accountSubsidy.enabled;
                rateAuditSwitch.checked = autoApproveConfig.rateAudit.enabled;
                subsidyPackageSwitch.checked = autoApproveConfig.subsidyPackage.enabled;
                withdrawRateSwitch.checked = autoApproveConfig.withdrawRate.enabled;
                
                // 设置金额阈值
                amountInput.value = autoApproveConfig.accountSubsidy.amount;
                
                // 显示/隐藏账户补贴配置区域
                if (autoApproveConfig.accountSubsidy.enabled) {
                    configDiv.style.display = 'block';
                } else {
                    configDiv.style.display = 'none';
                }
                
                // 绑定弹窗内的事件
                bindModalEvents();
                
                modal.style.display = 'block';
                
                // 点击遮罩关闭弹窗
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeAutoApproveModal();
                    }
                });
            } catch (error) {
                console.error('打开免审配置弹窗出错:', error);
                alert('打开配置弹窗失败，请刷新页面后重试');
            }
        }

        // 关闭免审配置弹窗
        function closeAutoApproveModal() {
            const modal = document.getElementById('auto-approve-modal');
            modal.style.display = 'none';
        }

        // 保存免审配置
        function saveAutoApproveConfig() {
            const accountSubsidySwitch = document.getElementById('account-subsidy-switch');
            const rateAuditSwitch = document.getElementById('rate-audit-switch');
            const subsidyPackageSwitch = document.getElementById('subsidy-package-switch');
            const withdrawRateSwitch = document.getElementById('withdraw-rate-switch');
            const amountInput = document.getElementById('approve-amount');
            
            // 如果账户补贴免审开启，验证金额
            if (accountSubsidySwitch.checked) {
                const amount = parseFloat(amountInput.value);
                if (!amount || amount < 1 || amount > 10000) {
                    alert('请输入有效的账户补贴免审金额（1-10000元）');
                    return;
                }
                autoApproveConfig.accountSubsidy.amount = amount;
            }
            
            // 更新配置
            autoApproveConfig.accountSubsidy.enabled = accountSubsidySwitch.checked;
            autoApproveConfig.rateAudit.enabled = rateAuditSwitch.checked;
            autoApproveConfig.subsidyPackage.enabled = subsidyPackageSwitch.checked;
            autoApproveConfig.withdrawRate.enabled = withdrawRateSwitch.checked;
            
            // 保存到存储
            saveConfigToStorage();
            
            // 更新显示
            updateAutoApproveDisplay();
            
            // 关闭弹窗
            closeAutoApproveModal();
            
            // 显示成功提示
            const enabledFeatures = [];
            if (autoApproveConfig.accountSubsidy.enabled) enabledFeatures.push('账户补贴免审');
            if (autoApproveConfig.rateAudit.enabled) enabledFeatures.push('费率审核免审');
            if (autoApproveConfig.subsidyPackage.enabled) enabledFeatures.push('补贴包免审');
            if (autoApproveConfig.withdrawRate.enabled) enabledFeatures.push('提现费率审核免审');
            
            if (enabledFeatures.length > 0) {
                showSuccessMessage(`${enabledFeatures.join('、')}配置保存成功`);
            } else {
                showSuccessMessage('免审配置已关闭');
            }
        }

        // 保存配置到localStorage（模拟后端保存）
        function saveConfigToStorage() {
            localStorage.setItem('autoApproveConfig', JSON.stringify(autoApproveConfig));
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            // 创建临时提示框
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 24px;
                right: 24px;
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                color: #389e0d;
                padding: 12px 16px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 3000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 3秒后自动消失
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 流程查看相关函数
        
        // 模拟的流程数据
        const processData = {
            '20241201001': {
                orderId: '20241201001',
                merchantName: '潮州市邮政便民服务点',
                applicant: '张三',
                amount: '500.00',
                submitTime: '2024-12-15 09:30',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '潮州市湘桥区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-15 09:30',
                        time: '2024-12-15 09:30',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '500元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入下一级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '潮州市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-15 09:31',
                        time: '2024-12-15 10:15',
                        operator: '李四',
                        isAutoApprove: false,
                        threshold: '300元',
                        content: '意见：经审核，商户资质良好，申请理由充分，予以通过。',
                        remark: '人工审核通过，进入省级审核'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-15 10:15',
                        time: '2024-12-15 10:16',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '意见：系统自动审核通过。',
                        remark: '审核流程完成，补贴已生效'
                    }
                ]
            },
            '20241201002': {
                orderId: '20241201002',
                merchantName: '汕头市邮政营业厅',
                applicant: '王五',
                amount: '800.00',
                submitTime: '2024-12-14 14:20',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '汕头市龙湖区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-14 14:20',
                        time: '2024-12-14 15:30',
                        operator: '赵六',
                        isAutoApprove: false,
                        threshold: '500元',
                        content: '意见：经审核，商户资质良好，申请理由充分，予以通过。',
                        remark: '人工审核通过，进入地市级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '汕头市邮政分公司',
                        status: 'current',
                        statusText: '未审核',
                        arrivalTime: '2024-12-14 15:30',
                        time: '',
                        operator: '',
                        isAutoApprove: false,
                        threshold: '300元',
                        content: '-',
                        remark: '当前审核节点'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '未审核',
                        time: '',
                        operator: '',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '-',
                        remark: ''
                    }
                ]
            },
            '20241201003': {
                orderId: '20241201003',
                merchantName: '梅州市邮政代办点',
                applicant: '赵六',
                amount: '1200.00',
                submitTime: '2024-12-13 16:45',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '梅州市梅江区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-13 16:45',
                        time: '2024-12-13 17:20',
                        operator: '孙八',
                        isAutoApprove: false,
                        threshold: '500元',
                        content: '意见：经审核，商户资质良好，申请理由充分，予以通过。',
                        remark: '人工审核通过，进入地市级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '梅州市邮政分公司',
                        status: 'rejected',
                        statusText: '审核驳回',
                        arrivalTime: '2024-12-13 17:20',
                        time: '2024-12-14 09:15',
                        operator: '钱七',
                        isAutoApprove: false,
                        threshold: '300元',
                        content: '意见：经审核，申请金额过大，商户近期已有多笔补贴申请，暂不予通过。',
                        remark: '审核被拒绝，流程结束'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'pending',
                        statusText: '未审核',
                        time: '',
                        operator: '',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '-',
                        remark: ''
                    }
                ]
            },
            '20241201004': {
                orderId: '20241201004',
                merchantName: '揭阳市邮政支局',
                applicant: '孙八',
                amount: '200.00',
                submitTime: '2024-12-12 11:30',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '揭阳市榕城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-12 11:30',
                        time: '2024-12-12 11:30',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '500元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入下一级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '揭阳市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        arrivalTime: '2024-12-12 11:30',
                        time: '2024-12-12 11:31',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '300元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入下一级审核'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'current',
                        statusText: '未审核',
                        arrivalTime: '2024-12-12 11:31',
                        time: '',
                        operator: '',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '-',
                        remark: '当前审核节点'
                    }
                ]
            },
            '20241201005': {
                orderId: '20241201005',
                merchantName: '清远市邮政便民点',
                applicant: '周九',
                amount: '150.00',
                submitTime: '2024-12-11 08:15',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '清远市清城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-11 08:15',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '500元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入下一级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '清远市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-11 08:15',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '300元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入下一级审核'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-11 08:15',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '意见：系统自动审核通过。',
                        remark: '符合免审条件，自动进入上游审核'
                    },
                    {
                        level: '上游审核',
                        institution: '拉卡拉支付股份有限公司',
                        status: 'current',
                        statusText: '未审核',
                        time: '',
                        operator: '',
                        isAutoApprove: false,
                        threshold: '',
                        content: '-',
                        remark: '当前审核节点'
                    }
                ]
            },
            '20241201006': {
                orderId: '20241201006',
                merchantName: '东莞市邮政代办点',
                applicant: '陈一',
                amount: '600.00',
                submitTime: '2024-12-10 16:20',
                steps: [
                    {
                        level: '区县级审核',
                        institution: '东莞市南城区邮政分局',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-10 17:30',
                        operator: '周九',
                        isAutoApprove: false,
                        threshold: '500元',
                        content: '意见：经审核，商户资质良好，申请理由充分，予以通过。',
                        remark: '人工审核通过，进入地市级审核'
                    },
                    {
                        level: '地市级审核',
                        institution: '东莞市邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-11 10:30',
                        operator: '李四',
                        isAutoApprove: false,
                        threshold: '300元',
                        content: '意见：经审核，商户经营状况良好，申请合理，予以通过。',
                        remark: '人工审核通过，进入省级审核'
                    },
                    {
                        level: '省级审核',
                        institution: '广东省邮政分公司',
                        status: 'completed',
                        statusText: '审核通过',
                        time: '2024-12-11 11:00',
                        operator: '系统自动',
                        isAutoApprove: true,
                        threshold: '1000元',
                        content: '意见：系统自动审核通过。',
                        remark: '审核流程完成，补贴已生效'
                    }
                ]
            }
        };

        // 显示流程查看弹窗
        function showProcessModal(orderId) {
            const modal = document.getElementById('process-modal');
            const data = processData[orderId];
            
            if (!data) {
                alert('未找到该申请的流程信息');
                return;
            }
            
            // 更新流程名称
            // document.getElementById('process-flow-name').textContent = '账户补贴审核流程';
            
            // 生成流程
            generateProcessFlow(data);
            
            // 显示弹窗
            modal.style.display = 'block';
            
            // 点击遮罩关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProcessModal();
                }
            });
        }

        // 关闭流程查看弹窗
        function closeProcessModal() {
            const modal = document.getElementById('process-modal');
            modal.style.display = 'none';
        }

        // 生成流程HTML
        function generateProcessFlow(data) {
            const timeline = document.getElementById('process-timeline');
            timeline.innerHTML = '';
            
            // 添加发起人节点
            const startItem = document.createElement('div');
            startItem.className = 'flow-item';
            startItem.innerHTML = `
                <div class="flow-node start">
                    <svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 6 9 17l-5-5"/>
                    </svg>
                </div>
                <div class="flow-content">
                                            <div class="flow-title">
                            <div class="flow-title-left">
                                <span>发起人</span>
                                <span class="flow-status completed">已提交</span>
                            </div>
                        </div>
                    <div class="flow-institution">${data.applicant}</div>
                    <div class="flow-description">意见：提交</div>
                    <div class="flow-meta">
                        <div class="flow-meta-item">
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                             </svg>
                             ${data.submitTime} 提交 
                            <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                            ${data.applicant}
                        </div>
                    </div>
                </div>
            `;
            timeline.appendChild(startItem);
            
            // 添加审核节点
            data.steps.forEach((step, index) => {
                const flowItem = document.createElement('div');
                flowItem.className = 'flow-item';
                
                let nodeClass = step.status;
                let statusClass = step.status;
                let nodeIcon = '';
                
                switch(step.status) {
                    case 'completed':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                        break;
                    case 'current':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                        // 不添加任何按钮
                        break;
                    case 'rejected':
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                        break;
                    default:
                        nodeIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
                }
                
                let statusText = step.statusText;
                if (step.status === 'current') {
                    statusText = '未审核';
                } else if (step.statusText === '审核驳回') {
                    statusText = '审核驳回';
                }
                
                flowItem.innerHTML = `
                    <div class="flow-node ${nodeClass}">
                        ${nodeIcon}
                    </div>
                    <div class="flow-content">
                        <div class="flow-title">
                            <div class="flow-title-left">
                                <span>${step.level}</span>
                                <span class="flow-status ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        <div class="flow-institution">${step.institution}</div>
                        <div class="flow-description">${step.content}</div>
                        <div class="flow-meta">
                            ${step.time ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                    ${step.time} ${step.status === 'completed' ? '审核' : ''}
                                </div>
                            ` : ''}
                            ${step.operator ? `
                                <div class="flow-meta-item">
                                    <svg style="width: 12px; height: 12px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                        <circle cx="12" cy="7" r="4"/>
                                    </svg>
                                    ${step.operator}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                
                timeline.appendChild(flowItem);
            });
            
                        // 添加结束节点
            const allCompleted = data.steps.every(step => step.status === 'completed');
            const hasRejected = data.steps.some(step => step.status === 'rejected');
            
            const endItem = document.createElement('div');
            endItem.className = 'flow-item';
            
            let endStatus = 'pending';
            let endText = '终审环节';
            let endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/></svg>';
            let endTime = '';
            
            if (allCompleted) {
                endStatus = 'completed';
                endText = '已完成';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 6 9 17l-5-5"/></svg>';
                // 获取最后一个完成步骤的时间作为结束时间
                const lastCompletedStep = data.steps.filter(step => step.status === 'completed').pop();
                if (lastCompletedStep && lastCompletedStep.time) {
                    endTime = lastCompletedStep.time;
                }
            } else if (hasRejected) {
                endStatus = 'rejected';
                endText = '流程终止';
                endIcon = '<svg style="width: 10px; height: 10px; color: white;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>';
                // 获取被拒绝步骤的时间作为终止时间
                const rejectedStep = data.steps.find(step => step.status === 'rejected');
                if (rejectedStep && rejectedStep.time) {
                    endTime = rejectedStep.time;
                }
            }
            
            endItem.innerHTML = `
                <div class="flow-node ${endStatus === 'completed' ? 'completed' : endStatus === 'rejected' ? 'rejected' : 'end'}">
                    ${endIcon}
                </div>
                <div class="flow-content">
                    <div class="flow-title">
                        <div class="flow-title-left">
                            <span>结束</span>
                            <span class="flow-status ${endStatus}">${endText}</span>
                        </div>
                        
                    </div>
                    <div class="flow-description">${endText}</div>
                </div>
            `;
            timeline.appendChild(endItem);
        }
    </script>
</body>
</html>
</html>