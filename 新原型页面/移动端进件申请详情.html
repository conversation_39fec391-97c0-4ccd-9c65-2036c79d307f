<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进件申请详情 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .detail-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .detail-item {
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #059669;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }
        

        
        .action-button {
            background: var(--postal-green);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            background: var(--postal-green-dark);
        }
        
        .action-button:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }

        /* 审核进度时间线样式 */
        .timeline {
            position: relative;
            padding-left: 32px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 16px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -24px;
            top: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #e5e7eb;
            background: white;
            z-index: 1;
        }

        .timeline-dot.active {
            background: var(--postal-green);
            border-color: var(--postal-green);
        }

        .timeline-dot.current {
            background: #3b82f6;
            border-color: #3b82f6;
            animation: pulse 2s infinite;
        }

        .timeline-dot.pending {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        .timeline-dot.rejected {
            border-color: #ef4444;
            background-color: #ef4444;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .info-box {
            background-color: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">进件申请详情</h1>
            <div class="w-10"></div>
        </div>
    </div>

    <!-- 申请状态卡片 -->
    <div class="px-4 py-4">
        <div class="detail-section p-4">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 postal-green-light rounded-full flex items-center justify-center">
                    <i class="fas fa-file-alt text-postal-green text-2xl"></i>
                </div>
                <h2 class="text-lg font-medium text-gray-900 mb-2">进件申请</h2>
                <div class="status-badge status-approved mb-4" id="statusBadge">已通过</div>
            </div>
        </div>
    </div>

    <!-- 审核信息 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">审核信息</h3>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请编号</span>
                <span class="text-gray-900 font-medium" id="applicationId">JJ202407190001</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请人</span>
                <span class="text-gray-900" id="applicantName">李四</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">市级分公司</span>
                <span class="text-gray-900" id="citycom">深圳市分公司</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">区县分公司</span>
                <span class="text-gray-900" id="xiancom">南山区分公司</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">营业所</span>
                <span class="text-gray-900" id="yys">南山营业所</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">申请时间</span>
                <span class="text-gray-900" id="applyTime">2024-07-19 16:45</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">更新时间</span>
                <span class="text-gray-900" id="updateTime">2024-07-20 10:30</span>
            </div>

            <div class="detail-item py-3">
                <div class="mb-2">
                    <span class="text-gray-600">驳回原因</span>
                </div>
                <p class="text-gray-900 text-sm leading-relaxed" id="rejectReason">
                    -
                </p>
            </div>
        </div>
    </div>

    <!-- 进件申请信息 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">进件申请信息</h3>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">商户负责人</span>
                <span class="text-gray-900" id="name">小张</span>
            </div>
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">负责人手机号</span>
                <span class="text-gray-900" id="phoneNumber">***********</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">负责人身份证号</span>
                <span class="text-gray-900" id="idCard">110101199001011234</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">银行卡号</span>
                <span class="text-gray-900" id="bankCard">6222021234567890123</span>
            </div>

            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">营业执照号</span>
                <span class="text-gray-900" id="businessLicense">91110101MA01234567</span>
            </div>

            <div class="detail-item py-3">
                <div class="mb-2">
                    <span class="text-gray-600">申请原因</span>
                </div>
                <p class="text-gray-900 text-sm leading-relaxed" id="applyReason">
                    新开设超市门店，需要开通微邮付收款服务，为顾客提供便捷的支付方式，提升经营效率。
                </p>
            </div>
        </div>
    </div>

    <!-- 申请进件时间 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">申请进件时间</h3>
            
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">开始时间</span>
                <span class="text-gray-900" id="startTime">2024-07-20 00:00</span>
            </div>
            
            <div class="detail-item py-3 flex justify-between">
                <span class="text-gray-600">结束时间</span>
                <span class="text-gray-900" id="endTime">2024-08-20 23:59</span>
            </div>
            
            <div class="info-box mt-3">
                <div class="flex items-start space-x-2">
                    <i class="fas fa-info-circle text-blue-500 text-sm mt-0.5"></i>
                    <p class="text-sm text-blue-700">
                        审批通过后，请尽快在时间范围内进件，否则超时需重新申请。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核进度 -->
    <div class="px-4 pb-4">
        <div class="detail-section p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">审核进度</h3>

            <div class="timeline" id="timeline">
                <!-- 时间轴内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="px-4 pb-6">
        <button class="action-button w-full" onclick="goBack()">
            <i class="fas fa-arrow-left mr-2"></i>
            返回
        </button>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 模拟申请详情数据
        const applicationDetails = {
            2: {
                id: 'JJ202407190001',
                applicant: '李四',
                merchantName: '油油果超市',
                merchantId: '**********',
                phone: '***********',
                idCard: '110101199001011234',
                bankCard: '6222021234567890123',
                businessLicense: '91110101MA01234567',
                applyTime: '2024-07-19 16:45',
                updateTime: '2024-07-20 10:30',
                rejectReason: '-',
                startTime: '2024-07-20 00:00',
                endTime: '2024-08-20 23:59',
                reason: '新开设超市门店，需要开通微邮付收款服务，为顾客提供便捷的支付方式，提升经营效率。',
                status: 'approved',
                citycom: '深圳市分公司',
                xiancom: '南山区分公司',
                yys: '南山营业所',
                name: '小张',
                timeline: [
                    { time: '2024-07-19 16:45', title: '提交申请', desc: '申请已提交，等待审核', operator: '李四', status: 'completed' },
                    { time: '2024-07-20 09:30', title: '初审通过', desc: '初步审核已通过', operator: '南山营业所', status: 'completed' },
                    { time: '2024-07-20 10:30', title: '终审通过', desc: '最终审核已通过', operator: '南山区分公司', status: 'completed' },
                    { time: '2024-07-20 10:30', title: '审核完成', desc: '审核通过，可以开始进件', operator: '系统', status: 'completed' }
                ]
            },
            9: {
                id: 'JJ202407120001',
                applicant: '陈小明',
                merchantName: '陈小明餐厅',
                merchantId: '**********',
                phone: '***********',
                idCard: '******************',
                bankCard: '6222021234567890456',
                businessLicense: '91440101MA01234568',
                applyTime: '2024-07-12 14:15',
                updateTime: '2024-07-13 11:20',
                rejectReason: '提交的营业执照信息与实际经营地址不符，请核实后重新提交申请。',
                startTime: '2024-07-13 00:00',
                endTime: '2024-08-13 23:59',
                reason: '新开餐厅，需要开通微邮付收款功能，方便顾客支付。',
                status: 'rejected',
                citycom: '广州市分公司',
                xiancom: '天河区分公司',
                yys: '天河营业所',
                name: '陈小明',
                timeline: [
                    { time: '2024-07-12 14:15', title: '提交申请', desc: '申请已提交，等待审核', operator: '陈小明', status: 'completed' },
                    { time: '2024-07-12 18:30', title: '初审通过', desc: '初步审核已通过', operator: '天河营业所', status: 'completed' },
                    { time: '2024-07-13 11:20', title: '终审驳回', desc: '提交的营业执照信息与实际经营地址不符，请核实后重新提交申请。', operator: '天河区分公司', status: 'completed' }
                ]
            }
        };

        // 返回按钮
        function goBack() {
            window.location.href = '移动端申请中心.html';
        }

        // 获取状态信息
        function getStatusInfo(status) {
            switch(status) {
                case 'pending':
                    return {
                        text: '审核中',
                        class: 'status-pending'
                    };
                case 'approved':
                    return {
                        text: '已通过',
                        class: 'status-approved'
                    };
                case 'rejected':
                    return {
                        text: '已驳回',
                        class: 'status-rejected'
                    };
                default:
                    return {
                        text: '未知状态',
                        class: 'status-pending'
                    };
            }
        }

        // 渲染时间轴
        function renderTimeline(timeline) {
            const container = document.getElementById('timeline');

            container.innerHTML = timeline.map(item => {
                let dotClass = 'timeline-dot';
                let dotContent = '';

                if (item.status === 'completed') {
                    if (item.title.includes('驳回')) {
                        dotClass += ' rejected';
                        dotContent = '<i class="fas fa-times"></i>';
                    } else {
                        dotClass += ' active';
                    }
                } else if (item.status === 'current') {
                    dotClass += ' current';
                }

                return `
                    <div class="timeline-item">
                        <div class="${dotClass}">${dotContent}</div>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <h4 class="font-medium text-gray-900 text-sm">${item.title}</h4>
                                ${item.time ? `<span class="text-xs text-gray-500">${item.time}</span>` : ''}
                            </div>
                            <p class="text-sm text-gray-600 mb-1">${item.desc}</p>
                            ${item.operator ? `<p class="text-xs text-gray-400">操作人：${item.operator}</p>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 加载申请详情
        function loadApplicationDetail() {
            const id = getUrlParameter('id') || '2';
            const detail = applicationDetails[id];

            if (!detail) {
                alert('申请详情不存在');
                goBack();
                return;
            }

            // 更新页面内容
            document.getElementById('applicationId').textContent = detail.id;
            document.getElementById('applicantName').textContent = detail.applicant;
            document.getElementById('applyTime').textContent = detail.applyTime;
            document.getElementById('updateTime').textContent = detail.updateTime;
            document.getElementById('rejectReason').textContent = detail.rejectReason;
            document.getElementById('citycom').textContent = detail.citycom;
            document.getElementById('xiancom').textContent = detail.xiancom;
            document.getElementById('yys').textContent = detail.yys;
            document.getElementById('name').textContent = detail.name;
            document.getElementById('phoneNumber').textContent = detail.phone;
            document.getElementById('idCard').textContent = detail.idCard;
            document.getElementById('bankCard').textContent = detail.bankCard;
            document.getElementById('businessLicense').textContent = detail.businessLicense;
            document.getElementById('startTime').textContent = detail.startTime;
            document.getElementById('endTime').textContent = detail.endTime;
            document.getElementById('applyReason').textContent = detail.reason;

            // 更新状态信息
            const statusInfo = getStatusInfo(detail.status);
            const statusBadge = document.getElementById('statusBadge');
            statusBadge.textContent = statusInfo.text;
            statusBadge.className = `status-badge ${statusInfo.class}`;

            // 渲染时间轴
            if (detail.timeline) {
                renderTimeline(detail.timeline);
            }

            // 根据状态和时间控制按钮显示
            const entryBtn = document.getElementById('entryBtn');
            if (detail.status === 'approved') {
                const canEntry = checkEntryTimeRange(detail.startTime, detail.endTime);
                if (!canEntry) {
                    entryBtn.disabled = true;
                    entryBtn.textContent = '进件时间已过期';
                    entryBtn.style.background = '#d1d5db';
                }
            } else {
                entryBtn.style.display = 'none';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadApplicationDetail();
        });
    </script>
</body>
</html>
