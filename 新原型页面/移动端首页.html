<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .function-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        }
        
        .function-card:active {
            transform: scale(0.95);
            box-shadow: 0 4px 12px rgba(0, 166, 81, 0.2);
        }
        
        .function-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .header-gradient {
            background: linear-gradient(135deg, var(--postal-green) 0%, var(--postal-green-dark) 100%);
        }
        
        .scan-button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .scan-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部用户信息区域 -->
    <div class="header-gradient text-white px-4 py-6 relative overflow-hidden">
        <!-- 装饰性背景图案 -->
        <div class="absolute top-0 right-0 w-32 h-32 opacity-10">
            <i class="fas fa-envelope text-8xl"></i>
        </div>
        
        <div class="relative z-10">
            <!-- 顶部导航 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-home text-xl"></i>
                    <span class="font-medium">微邮付管家</span>
                </div>
                <button class="scan-button flex items-center space-x-2" onclick="openScanner()">
                    <i class="fas fa-qrcode"></i>
                    <span>扫码网点</span>
                </button>
            </div>
            
            <!-- 用户信息 -->
            <div class="flex items-center space-x-4">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiM0Qjc2ODgiLz4KPHN2ZyB4PSIxNSIgeT0iMTUiIHdpZHRoPSIzMCIgaGVpZ2h0PSIzMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkgxNVYxNi41SDlWMjJIM1Y5QzMgNy45IDMuOSA3IDUgN0g5LjVDMTAuNSA3IDExLjQgNy40IDEyIDhDMTIuNiA3LjQgMTMuNSA3IDE0LjUgN0gxOUMyMC4xIDcgMjEgNy45IDIxIDlaIi8+Cjwvc3ZnPgo8L3N2Zz4K" 
                     alt="用户头像" 
                     class="user-avatar">
                <div>
                    <h2 class="text-lg font-medium">Hi, 兄心</h2>
                    <p class="text-sm opacity-90">18435141411</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能菜单网格 -->
    <div class="px-4 py-6">
        <div class="grid grid-cols-2 gap-4">
            <!-- 进件开户 -->
            <div class="function-card p-6 text-center" onclick="goToMerchantEntry()">
                <div class="function-icon bg-blue-100 text-blue-600">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h3 class="font-medium text-gray-900">进件开户</h3>
            </div>
            
            <!-- 商户管理 -->
            <div class="function-card p-6 text-center" onclick="goToMerchantManagement()">
                <div class="function-icon bg-green-100 text-green-600">
                    <i class="fas fa-users-cog"></i>
                </div>
                <h3 class="font-medium text-gray-900">商户管理</h3>
            </div>
            
            <!-- 交易统计 -->
            <div class="function-card p-6 text-center" onclick="goToTransactionStats()">
                <div class="function-icon bg-purple-100 text-purple-600">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="font-medium text-gray-900">交易统计</h3>
            </div>
            
            <!-- 门店管理 -->
            <div class="function-card p-6 text-center" onclick="goToStoreManagement()">
                <div class="function-icon bg-orange-100 text-orange-600">
                    <i class="fas fa-store"></i>
                </div>
                <h3 class="font-medium text-gray-900">门店管理</h3>
            </div>
            
            <!-- 码牌设备 -->
            <div class="function-card p-6 text-center" onclick="goToDeviceManagement()">
                <div class="function-icon bg-indigo-100 text-indigo-600">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="font-medium text-gray-900">码牌设备</h3>
            </div>
            
            <!-- 剪单政策 -->
            <div class="function-card p-6 text-center" onclick="goToPolicy()">
                <div class="function-icon bg-teal-100 text-teal-600">
                    <i class="fas fa-file-contract"></i>
                </div>
                <h3 class="font-medium text-gray-900">费率政策</h3>
            </div>
            
            <!-- 砸一下数据看板 -->
            <div class="function-card p-6 text-center" onclick="goToDataDashboard()">
                <div class="function-icon bg-cyan-100 text-cyan-600">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3 class="font-medium text-gray-900">碰一下数据看板</h3>
            </div>
            
            <!-- 砸一下龙虎榜 -->
            <div class="function-card p-6 text-center" onclick="goToLeaderboard()">
                <div class="function-icon bg-red-100 text-red-600">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3 class="font-medium text-gray-900">碰一下龙虎榜</h3>
            </div>
            
            <!-- 补贴计算器 -->
            <div class="function-card p-6 text-center" onclick="goToCalculator()">
                <div class="function-icon bg-yellow-100 text-yellow-600">
                    <i class="fas fa-calculator"></i>
                </div>
                <h3 class="font-medium text-gray-900">补贴计算器</h3>
            </div>
            
            <!-- 任务管理 -->
            <div class="function-card p-6 text-center" onclick="goToTaskManagement()">
                <div class="function-icon bg-pink-100 text-pink-600">
                    <i class="fas fa-tasks"></i>
                </div>
                <h3 class="font-medium text-gray-900">任务管理</h3>
            </div>
            
            <!-- 申请中心 -->
            <div class="function-card p-6 text-center" onclick="goToApplicationCenter()">
                <div class="function-icon postal-green-light text-postal-green">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3 class="font-medium text-gray-900">申请中心</h3>
            </div>
            
            <!-- 个人中心 -->
            <div class="function-card p-6 text-center" onclick="goToPersonalCenter()">
                <div class="function-icon bg-gray-100 text-gray-600">
                    <i class="fas fa-user"></i>
                </div>
                <h3 class="font-medium text-gray-900">个人中心</h3>
            </div>
        </div>
    </div>

    <script>
        // 扫码功能
        function openScanner() {
            alert('扫码功能开发中...');
        }

        // 进件开户
        function goToMerchantEntry() {
            window.location.href = '移动端进件申请.html';
        }

        // 商户管理
        function goToMerchantManagement() {
            alert('商户管理功能开发中...');
        }

        // 交易统计
        function goToTransactionStats() {
            alert('交易统计功能开发中...');
        }

        // 门店管理
        function goToStoreManagement() {
            alert('门店管理功能开发中...');
        }

        // 码牌设备
        function goToDeviceManagement() {
            window.location.href = '移动端设备管理.html';
        }

        // 剪单政策
        function goToPolicy() {
            alert('剪单政策功能开发中...');
        }

        // 数据看板
        function goToDataDashboard() {
            alert('数据看板功能开发中...');
        }

        // 龙虎榜
        function goToLeaderboard() {
            alert('龙虎榜功能开发中...');
        }

        // 补贴计算器
        function goToCalculator() {
            alert('补贴计算器功能开发中...');
        }

        // 任务管理
        function goToTaskManagement() {
            alert('任务管理功能开发中...');
        }

        // 申请中心
        function goToApplicationCenter() {
            window.location.href = '移动端申请中心.html';
        }

        // 个人中心
        function goToPersonalCenter() {
            alert('个人中心功能开发中...');
        }
    </script>
</body>
</html>
