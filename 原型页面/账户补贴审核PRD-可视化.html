<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户补贴审核管理系统 - PRD文档</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }
        .hero-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .section-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=40&h=40&fit=crop&crop=center" alt="微邮付" class="w-10 h-10 rounded-lg">
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">微邮付</h1>
                        <p class="text-sm text-gray-600">账户补贴审核管理系统</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full font-medium">PRD v3.0</span>
                    <span class="text-sm text-gray-500">2025-06-17</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-bg text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl font-bold mb-6">账户补贴审核管理系统</h1>
            <p class="text-xl mb-8 max-w-3xl mx-auto opacity-90">
                智能化三级机构审核体系，基于金额阈值的自动免审机制，提升审核效率90%以上
            </p>
            <div class="flex justify-center space-x-6">
                <div class="text-center">
                    <div class="text-3xl font-bold">3级</div>
                    <div class="text-sm opacity-80">审核机构</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">4种</div>
                    <div class="text-sm opacity-80">免审类型</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">90%+</div>
                    <div class="text-sm opacity-80">效率提升</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold">实时</div>
                    <div class="text-sm opacity-80">流程追踪</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 目录导航 -->
    <section class="bg-white py-12 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">文档目录</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="#background" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 16v-4"/>
                        <path d="M12 8h.01"/>
                    </svg>
                    <div class="font-medium">项目背景</div>
                </a>
                <a href="#goals" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 6v6l4 2"/>
                    </svg>
                    <div class="font-medium">产品目标</div>
                </a>
                <a href="#functions" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                    <div class="font-medium">功能需求</div>
                </a>
                <a href="#workflow" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-cyan-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <circle cx="12" cy="12" r="4"/>
                    </svg>
                    <div class="font-medium">流程展示</div>
                </a>
                <a href="#process" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-orange-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <polyline points="7.5,4.21 12,6.81 16.5,4.21"/>
                        <polyline points="7.5,19.79 7.5,14.6 3,12"/>
                        <polyline points="21,12 16.5,14.6 16.5,19.79"/>
                    </svg>
                    <div class="font-medium">业务流程</div>
                </a>
                <a href="#design" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-pink-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                    </svg>
                    <div class="font-medium">界面设计</div>
                </a>
                <a href="#tech" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-indigo-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="16,18 22,12 16,6"/>
                        <polyline points="8,6 2,12 8,18"/>
                    </svg>
                    <div class="font-medium">技术实现</div>
                </a>
                <a href="#risk" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-red-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                    </svg>
                    <div class="font-medium">风险控制</div>
                </a>
                <a href="#testing" class="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6 mb-2 text-teal-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/>
                    </svg>
                    <div class="font-medium">测试用例</div>
                </a>
            </div>
        </div>
    </section>

    <!-- 项目背景 -->
    <section id="background" class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-blue-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 16v-4"/>
                        <path d="M12 8h.01"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">项目背景</h2>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">业务背景</h3>
                        <p class="text-gray-700 leading-relaxed mb-6">
                            账户补贴审核是微邮付系统中的核心业务流程，采用区县、地市、省级三级逐级审核制度。
                            为提高审核效率，各级机构可独立配置免审金额阈值，在保证业务安全的同时，
                            实现小额补贴申请的快速处理。整个审核流程需要完整经过三级审核，确保业务合规性。
                        </p>
                        
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">问题描述</h3>
                        <div class="space-y-3">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">审核效率低：</strong>
                                    <span class="text-gray-700">每级机构都需要人工审核，处理时间长</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">流程不够灵活：</strong>
                                    <span class="text-gray-700">缺乏各级机构独立的免审机制，无法根据实际情况调整</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">审核标准不统一：</strong>
                                    <span class="text-gray-700">各级机构缺乏统一的免审规则配置平台</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">流程追踪困难：</strong>
                                    <span class="text-gray-700">难以实时掌握审核进度和各级处理状态</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=500&h=300&fit=crop&crop=center" 
                             alt="业务流程" class="w-full h-64 object-cover rounded-lg mb-6">
                        
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                            <h3 class="text-xl font-semibold text-gray-900 mb-4">解决方案</h3>
                            <p class="text-gray-700 leading-relaxed">
                                通过引入条件化免审机制，在保证风险可控的前提下，大幅提升小额补贴审核效率。
                                基于三级机构金额阈值的智能路由算法，实现自动化审核决策。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品目标 -->
    <section id="goals" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-green-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 6v6l4 2"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">产品目标</h2>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8 mb-12">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">核心目标</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <svg class="w-6 h-6 text-yellow-500 mt-1 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">流程优化：</strong>
                                    <span class="text-gray-700">实现逐级审核，确保每级机构都能独立决策</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-6 h-6 text-green-500 mt-1 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                    <path d="M9 12l2 2 4-4"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">灵活配置：</strong>
                                    <span class="text-gray-700">各级机构可独立设置免审金额，提升审核效率</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-6 h-6 text-blue-500 mt-1 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                    <circle cx="9" cy="7" r="4"/>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">全程监控：</strong>
                                    <span class="text-gray-700">实时追踪审核状态，清晰展示各级处理进度</span>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg class="w-6 h-6 text-pink-500 mt-1 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                </svg>
                                <div>
                                    <strong class="text-gray-900">体验提升：</strong>
                                    <span class="text-gray-700">通过免审机制加速审核流程，提升审批效率</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop&crop=center" 
                             alt="目标达成" class="w-full h-64 object-cover rounded-lg">
                    </div>
                </div>
                
                <h3 class="text-xl font-semibold text-gray-900 mb-6">量化指标</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
                        <div class="text-3xl font-bold text-blue-600 mb-2">90%+</div>
                        <div class="text-gray-700 font-medium">小额补贴审核效率提升</div>
                    </div>
                    <div class="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
                        <div class="text-3xl font-bold text-green-600 mb-2">60%+</div>
                        <div class="text-gray-700 font-medium">审核人员工作量减少</div>
                    </div>
                    <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
                        <div class="text-3xl font-bold text-purple-600 mb-2">95%+</div>
                        <div class="text-gray-700 font-medium">商户满意度提升</div>
                    </div>
                    <div class="text-center p-6 bg-gradient-to-br from-red-50 to-red-100 rounded-xl">
                        <div class="text-3xl font-bold text-red-600 mb-2">0.1%</div>
                        <div class="text-gray-700 font-medium">风险事件发生率控制</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能需求 -->
    <section id="functions" class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-purple-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">功能需求</h2>
                </div>
                
                <div class="grid md:grid-cols-1 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">政策审核功能</h3>
                        <div class="space-y-4">
                            <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                                <h4 class="font-semibold text-gray-900 mb-2">审核列表管理</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• 支持按商户号、审核状态、市级分公司、审核类型等条件筛选</li>
                                    <li>• 展示商户信息、申请人、提交时间、更新时间、生效时间等关键信息</li>
                                    <li>• 支持批量审核通过操作，提升处理效率</li>
                                    <li>• 实时显示三级审核状态：未审核、审核通过、审核驳回</li>
                                    <li>• 支持详情查看和完整流程追踪</li>
                                </ul>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                                <h4 class="font-semibold text-gray-900 mb-2">多类型免审配置</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• <strong>账户补贴免审：</strong>支持金额阈值配置</li>
                                    <li>• <strong>费率审核免审：</strong>支持开启/关闭功能</li>
                                    <li>• <strong>补贴包免审：</strong>支持开启/关闭功能</li>
                                    <li>• <strong>提现费率审核免审：</strong>支持开启/关闭功能</li>
                                    <li>• 独立配置开关，灵活组合使用</li>
                                    <li>• 实时状态回显，智能提示说明</li>
                                </ul>
                            </div>
                            
                            <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                                <h4 class="font-semibold text-gray-900 mb-2">流程查看功能</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• 完整展示三级审核流程时间线，清晰的节点状态</li>
                                    <li>• 时间信息优化：送达时间右对齐，处理时间下方显示</li>
                                    <li>• 去除技术细节，专注业务流程展示</li>
                                    <li>• 支持响应式设计，适配不同屏幕尺寸</li>
                                    <li>• 实时更新审核进度和当前所在节点</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    
                </div>
                
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">核心功能特性</h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <svg class="w-12 h-12 text-purple-600 mx-auto mb-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                            <h4 class="font-semibold text-gray-900 mb-2">智能审核</h4>
                            <p class="text-sm text-gray-600">基于金额阈值的自动化审核决策</p>
                        </div>
                        <div class="text-center">
                            <svg class="w-12 h-12 text-blue-600 mx-auto mb-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                <path d="M9 12l2 2 4-4"/>
                            </svg>
                            <h4 class="font-semibold text-gray-900 mb-2">安全可控</h4>
                            <p class="text-sm text-gray-600">多级审核保障，风险可控</p>
                        </div>
                        <div class="text-center">
                            <svg class="w-12 h-12 text-green-600 mx-auto mb-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                <line x1="9" y1="9" x2="9.01" y2="9"/>
                                <line x1="15" y1="9" x2="15.01" y2="9"/>
                            </svg>
                            <h4 class="font-semibold text-gray-900 mb-2">用户友好</h4>
                            <p class="text-sm text-gray-600">直观的流程展示和操作体验</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 流程展示功能 -->
    <section id="workflow" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-cyan-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <circle cx="12" cy="12" r="4"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">审核流程展示功能</h2>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">流程时间线设计</h3>
                        <div class="bg-white p-6 rounded-lg border shadow-sm">
                            <h4 class="font-semibold text-gray-900 mb-4">核心设计理念</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M20 6 9 17l-5-5"/>
                                    </svg>
                                    <div>
                                        <strong class="text-gray-900">清晰的时间轴：</strong>
                                        <span class="text-gray-700">垂直时间线展示，直观显示审核进度</span>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-blue-500 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                    <div>
                                        <strong class="text-gray-900">状态区分：</strong>
                                        <span class="text-gray-700">不同颜色和图标表示各种审核状态</span>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-purple-500 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M12 6v6l4 2"/>
                                    </svg>
                                    <div>
                                        <strong class="text-gray-900">时间信息：</strong>
                                        <span class="text-gray-700">送达时间右对齐，处理时间在下方显示</span>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-orange-500 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    <div>
                                        <strong class="text-gray-900">简洁信息：</strong>
                                        <span class="text-gray-700">去除技术细节，专注业务流程</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">界面原型预览</h3>
                        <div class="bg-white p-6 rounded-lg border shadow-sm">
                            <div class="text-sm text-gray-600 mb-4">流程：账户补贴审核流程 (版本：3)</div>
                            
                            <!-- 模拟流程节点 -->
                            <div class="relative pl-8">
                                <!-- 时间线 -->
                                <div class="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                                
                                <!-- 发起人节点 -->
                                <div class="relative mb-6">
                                    <div class="absolute left-[-22px] top-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                        <svg class="w-2 h-2 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                            <path d="M20 6 9 17l-5-5"/>
                                        </svg>
                                    </div>
                                    <div class="flex justify-between items-start">
                                        <div class="flex items-center gap-2">
                                            <span class="font-medium text-gray-900">发起人</span>
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">已提交</span>
                                        </div>
                                        <span class="text-xs text-gray-500">2024-12-15 09:30 送达</span>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1">张三</div>
                                    <div class="text-sm text-gray-500 mt-1">意见：提交</div>
                                    <div class="flex items-center gap-4 text-xs text-gray-400 mt-2">
                                        <span>2024-12-15 09:30 提交</span>
                                    </div>
                                </div>
                                
                                <!-- 区县级审核节点 -->
                                <div class="relative mb-6">
                                    <div class="absolute left-[-22px] top-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                        <svg class="w-2 h-2 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                            <path d="M20 6 9 17l-5-5"/>
                                        </svg>
                                    </div>
                                    <div class="flex justify-between items-start">
                                        <div class="flex items-center gap-2">
                                            <span class="font-medium text-gray-900">区县级审核</span>
                                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">审核通过</span>
                                        </div>
                                        <span class="text-xs text-gray-500">2024-12-15 09:30 送达</span>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1">潮州市湘桥区邮政分局</div>
                                    <div class="text-sm text-gray-500 mt-1">申请金额500元，未超过区县级免审阈值500元，系统自动审核通过。</div>
                                    <div class="flex items-center gap-4 text-xs text-gray-400 mt-2">
                                        <span>2024-12-15 09:30 审核</span>
                                        <span>系统自动</span>
                                    </div>
                                </div>
                                
                                <!-- 地市级审核节点 -->
                                <div class="relative mb-6">
                                    <div class="absolute left-[-22px] top-1 w-4 h-4 bg-gray-400 rounded-full border-2 border-white flex items-center justify-center">
                                        <svg class="w-2 h-2 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div class="flex justify-between items-start">
                                        <div class="flex items-center gap-2">
                                            <span class="font-medium text-gray-900">地市级审核</span>
                                            <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">未审核</span>
                                        </div>
                                        <span class="text-xs text-gray-500">2024-12-15 09:31 送达</span>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1">潮州市邮政分公司</div>
                                    <div class="text-sm text-gray-500 mt-1">申请金额500元，超过地市级免审阈值300元，需要人工审核。</div>
                                </div>
                                
                                <!-- 省级审核节点 -->
                                <div class="relative">
                                    <div class="absolute left-[-22px] top-1 w-4 h-4 bg-gray-400 rounded-full border-2 border-white flex items-center justify-center">
                                        <svg class="w-2 h-2 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div class="flex justify-between items-start">
                                        <div class="flex items-center gap-2">
                                            <span class="font-medium text-gray-900">省级审核</span>
                                            <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">未审核</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1">广东省邮政分公司</div>
                                    <div class="text-sm text-gray-500 mt-1">等待上级审核完成后进入本级审核。</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">流程展示优化特性</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">界面优化</h4>
                            <ul class="space-y-2 text-gray-700 text-sm">
                                <li>• <strong>统一状态用词：</strong>"审核中"改为"未审核"，"自动通过"改为"审核通过"</li>
                                <li>• <strong>优化时间布局：</strong>送达时间右对齐，处理时间下方显示</li>
                                <li>• <strong>简化技术细节：</strong>去除复杂的技术标签，专注业务流程</li>
                                <li>• <strong>颜色系统优化：</strong>未审核使用灰色，审核通过绿色，审核驳回红色</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">功能增强</h4>
                            <ul class="space-y-2 text-gray-700 text-sm">
                                <li>• <strong>多类型免审：</strong>支持账户补贴、费率审核、提现费率三种免审类型</li>
                                <li>• <strong>独立配置：</strong>各免审类型独立开关，灵活组合使用</li>
                                <li>• <strong>智能提示：</strong>仅账户补贴免审显示金额阈值配置和说明</li>
                                <li>• <strong>状态回显：</strong>配置后在列表页实时显示各类免审状态</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 业务流程示例 -->
    <section id="process" class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-orange-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <polyline points="7.5,4.21 12,6.81 16.5,4.21"/>
                        <polyline points="7.5,19.79 7.5,14.6 3,12"/>
                        <polyline points="21,12 16.5,14.6 16.5,19.79"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">账户补贴逐级审核流程示例</h2>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">各级机构免审配置</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg border-l-4 border-green-500">
                            <div class="flex justify-between items-center">
                                <div class="font-semibold text-gray-900">区县机构</div>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">免审已开启</span>
                            </div>
                            <div class="text-2xl font-bold text-green-600">500元</div>
                            <div class="text-sm text-gray-600">免审金额阈值</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border-l-4 border-yellow-500">
                            
                            <div class="flex justify-between items-center">
                                <div class="font-semibold text-gray-900">地市机构</div>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">免审已开启</span>
                            </div>
                            <div class="text-2xl font-bold text-yellow-600">300元</div>
                            <div class="text-sm text-gray-600">免审金额阈值</div>
                        </div>
                        <div class="bg-white p-4 rounded-lg border-l-4 border-red-500">
                            
                            <div class="flex justify-between items-center">
                                <div class="font-semibold text-gray-900">省级机构</div>
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">免审已开启</span>
                            </div>
                            <div class="text-2xl font-bold text-red-600">1000元</div>
                            <div class="text-sm text-gray-600">免审金额阈值</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 p-6 rounded-lg mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">申请案例：客户经理给商户申请充值 400元 补贴</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold mr-4">1</div>
                            <div class="flex-1">
                                <strong>区县审核：</strong>400 < 500，满足区县免审条件
                                <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">✓ 自动通过</span>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold mr-4">2</div>
                            <div class="flex-1">
                                <strong>地市审核：</strong>400 > 300，超过地市免审金额
                                <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">需要人工审核</span>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold mr-4">3</div>
                            <div class="flex-1">
                                <strong>省级审核：</strong>400 < 1000，满足省级免审条件
                                <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">✓ 自动通过</span>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold mr-4">结果</div>
                            <div class="flex-1">
                                <strong>审核流程：</strong>区县自动通过 → 地市人工审核 → 省级自动通过 → 最终审核完成
                                <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">需要地市人工审核</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">审核流程说明</h3>
                    <div class="space-y-3 text-gray-700">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-purple-600 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                            <p><strong>逐级审核：</strong>所有补贴申请必须经过区县→地市→省级三级审核，审核通过后才能最终生效</p>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-purple-600 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                            <p><strong>独立免审：</strong>各级机构独立配置免审金额，互不影响。申请金额低于配置的免审金额时自动通过，高于时需要人工审核</p>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-purple-600 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                            <p><strong>审核权限：</strong>每级机构都有完整的审核权限，可以选择通过或驳回申请</p>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-purple-600 mt-1 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                            </svg>
                            <p><strong>免审判断：</strong>基于单次申请充值金额进行判断，不考虑历史累计金额</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术实现 -->
    <section id="tech" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <img src="https://unpkg.com/lucide-static@latest/icons/code.svg" class="w-8 h-8 text-indigo-600 mr-4">
                    <h2 class="text-3xl font-bold gradient-text">技术实现</h2>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">免审配置数据结构</h3>
                        <div class="code-block p-4 rounded-lg overflow-x-auto">
                            <pre class="text-sm"><code>// 前端配置对象结构
const autoApproveConfig = {
    accountSubsidy: {
        enabled: true,      // 账户补贴免审开关
        amount: 500         // 免审金额阈值
    },
    rateAudit: {
        enabled: false      // 费率审核免审开关
    },
    subsidyPackage: {
        enabled: false      // 补贴包免审开关
    },
    withdrawRate: {
        enabled: true       // 提现费率审核免审开关
    }
};</code></pre>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">配置管理核心逻辑</h3>
                        <div class="code-block p-4 rounded-lg overflow-x-auto">
                            <pre class="text-sm"><code>// 配置保存逻辑
function saveAutoApproveConfig() {
    try {
        ensureConfigStructure(); // 确保配置结构完整
        
        const config = {
            accountSubsidy: {
                enabled: document.getElementById('accountSubsidySwitch').checked,
                amount: parseFloat(document.getElementById('amountThreshold').value) || 500
            },
            rateAudit: {
                enabled: document.getElementById('rateAuditSwitch').checked
            },
            subsidyPackage: {
                enabled: document.getElementById('subsidyPackageSwitch').checked
            },
            withdrawRate: {
                enabled: document.getElementById('withdrawRateSwitch').checked
            }
        };
        
        // 保存到本地存储
        localStorage.setItem('autoApproveConfig', JSON.stringify(config));
        
        // 更新界面显示
        updateConfigDisplay();
        
        showToast('配置保存成功', 'success');
    } catch (error) {
        console.error('保存配置失败:', error);
        showToast('保存配置失败', 'error');
    }
}</code></pre>
                        </div>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">流程展示数据结构</h3>
                        <div class="code-block p-4 rounded-lg overflow-x-auto">
                            <pre class="text-sm"><code>const processData = {
    orderId: '20241201001',
    merchantName: '潮州市邮政便民服务点',
    applicant: '张三',
    amount: '500.00',
    submitTime: '2024-12-15 09:30',
    steps: [
        {
            level: '区县级审核',
            institution: '潮州市湘桥区邮政分局',
            status: 'completed',
            statusText: '审核通过',      // 统一改为"审核通过"
            arrivalTime: '2024-12-15 09:30',
            time: '2024-12-15 09:30',
            operator: '系统自动',
            content: '申请金额500元，系统自动审核通过。'
        },
        {
            level: '地市级审核',
            institution: '潮州市邮政分公司',
            status: 'current',
            statusText: '未审核',       // "审核中"改为"未审核"
            arrivalTime: '2024-12-15 09:31',
            content: '申请金额500元，超过地市级免审阈值300元，需要人工审核。'
        }
    ]
};</code></pre>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">配置界面更新逻辑</h3>
                        <div class="code-block p-4 rounded-lg overflow-x-auto">
                            <pre class="text-sm"><code>// 更新配置显示状态
function updateConfigDisplay() {
    const config = getAutoApproveConfig();
    const statusContainer = document.getElementById('autoApproveStatus');
    
    // 清空现有状态
    statusContainer.innerHTML = '';
    
    // 显示各类免审状态
    if (config.accountSubsidy?.enabled) {
        statusContainer.appendChild(createStatusItem(
            '账户补贴免审', 
            `≤${config.accountSubsidy.amount}元`, 
            true
        ));
    }
    
    if (config.rateAudit?.enabled) {
        statusContainer.appendChild(createStatusItem('费率审核免审', '', false));
    }
    
    if (config.subsidyPackage?.enabled) {
        statusContainer.appendChild(createStatusItem('补贴包免审', '', false));
    }
    
    if (config.withdrawRate?.enabled) {
        statusContainer.appendChild(createStatusItem('提现费率审核免审', '', false));
    }
    
    // 如果没有启用任何免审，显示配置按钮
    if (!hasAnyAutoApproveEnabled(config)) {
        statusContainer.appendChild(createConfigButton());
    }
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 界面设计 -->
    <section id="design" class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-pink-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">界面设计</h2>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">多类型免审配置界面</h3>
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-3">配置入口设计</h4>
                            <ul class="space-y-2 text-gray-700 text-sm mb-4">
                                <li>• <strong>位置：</strong>政策审核页面右上角，独立的配置区域</li>
                                <li>• <strong>默认状态：</strong>仅显示"自动免审配置"按钮</li>
                                <li>• <strong>配置后状态：</strong>显示已启用的免审类型状态</li>
                                <li>• <strong>智能提示：</strong>仅账户补贴免审显示金额和说明图标</li>
                            </ul>
                            
                            <h4 class="font-semibold text-gray-800 mb-3">配置弹窗设计</h4>
                            <div class="bg-white p-4 rounded border">
                                <div class="text-sm text-gray-600">
                                    <div class="font-medium mb-2">弹窗标题：自动免审配置 (宽度：600px)</div>
                                    <div class="mb-2"><strong>账户补贴免审：</strong>开关 + 金额输入框（条件显示）</div>
                                    <div class="mb-2"><strong>费率审核免审：</strong>独立开关</div>
                                    <div class="mb-2"><strong>补贴包免审：</strong>独立开关</div>
                                    <div class="mb-2"><strong>提现费率审核免审：</strong>独立开关</div>
                                    <div><strong>布局特点：</strong>左对齐布局，边框分隔，独立配置</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <img src="https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=500&h=300&fit=crop&crop=center" 
                             alt="界面设计" class="w-full h-64 object-cover rounded-lg mb-4">
                        
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">状态展示设计</h3>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <svg class="w-5 h-5 text-green-600 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                <span class="text-green-800 font-medium">账户补贴免审 ≤500元 ⓘ</span>
                            </div>
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <svg class="w-5 h-5 text-blue-600 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                <span class="text-blue-800 font-medium">费率审核免审</span>
                            </div>
                            <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                                <svg class="w-5 h-5 text-orange-600 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                <span class="text-orange-800 font-medium">补贴包免审</span>
                            </div>
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                <svg class="w-5 h-5 text-purple-600 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 6 9 17l-5-5"/>
                                </svg>
                                <span class="text-purple-800 font-medium">提现费率审核免审</span>
                            </div>
                            <div class="bg-gray-100 p-3 rounded-lg text-sm text-gray-600">
                                <strong>显示规则：</strong>仅显示已启用的免审类型，未启用时显示配置按钮
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 测试用例 -->
    <section id="testing" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-teal-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">测试用例</h2>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">多类型配置测试</h3>
                        <div class="space-y-3">
                            <div class="bg-white p-3 rounded border-l-4 border-blue-500">
                                <h4 class="font-medium text-gray-900 mb-1">独立开关测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 账户补贴免审开关，验证金额字段显隐</li>
                                    <li>• 费率审核免审开关，验证独立控制</li>
                                    <li>• 补贴包免审开关，验证独立控制</li>
                                    <li>• 提现费率审核免审开关，验证独立控制</li>
                                    <li>• 组合配置测试，验证多种免审同时启用</li>
                                </ul>
                            </div>
                            <div class="bg-white p-3 rounded border-l-4 border-blue-500">
                                <h4 class="font-medium text-gray-900 mb-1">配置保存测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 验证配置数据结构完整性</li>
                                    <li>• 验证异常情况下的错误处理</li>
                                    <li>• 验证配置状态的实时回显</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-green-800 mb-4">界面交互测试</h3>
                        <div class="space-y-3">
                            <div class="bg-white p-3 rounded border-l-4 border-green-500">
                                <h4 class="font-medium text-gray-900 mb-1">状态显示测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 验证未配置时只显示配置按钮</li>
                                    <li>• 验证配置后状态的正确显示</li>
                                    <li>• 验证账户补贴免审的金额和提示图标</li>
                                    <li>• 验证其他免审类型的简洁显示</li>
                                </ul>
                            </div>
                            <div class="bg-white p-3 rounded border-l-4 border-green-500">
                                <h4 class="font-medium text-gray-900 mb-1">流程展示测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 验证"未审核"状态的灰色显示</li>
                                    <li>• 验证"审核通过"状态的绿色显示</li>
                                    <li>• 验证时间布局的优化效果</li>
                                    <li>• 验证响应式设计的适配性</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-purple-800 mb-4">错误处理测试</h3>
                        <div class="space-y-3">
                            <div class="bg-white p-3 rounded border-l-4 border-purple-500">
                                <h4 class="font-medium text-gray-900 mb-1">数据异常测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 验证配置对象结构异常的处理</li>
                                    <li>• 验证DOM元素不存在时的处理</li>
                                    <li>• 验证事件绑定异常的处理</li>
                                </ul>
                            </div>
                            <div class="bg-white p-3 rounded border-l-4 border-purple-500">
                                <h4 class="font-medium text-gray-900 mb-1">用户体验测试</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 验证操作反馈的及时性</li>
                                    <li>• 验证错误提示的友好性</li>
                                    <li>• 验证加载状态的处理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 风险控制 -->
    <section id="risk" class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="section-card rounded-2xl p-8 shadow-xl">
                <div class="flex items-center mb-8">
                    <svg class="w-8 h-8 text-red-600 mr-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                    </svg>
                    <h2 class="text-3xl font-bold gradient-text">风险控制</h2>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-red-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-red-800 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                                <line x1="12" y1="9" x2="12" y2="13"/>
                                <line x1="12" y1="17" x2="12.01" y2="17"/>
                            </svg>
                            业务风险
                        </h3>
                        <ul class="space-y-2 text-red-700">
                            <li>• 金额风险：设置最大免审金额上限</li>
                            <li>• 频次风险：监控异常申请频次</li>
                            <li>• 商户风险：建立商户信用评级机制</li>
                        </ul>
                    </div>
                    
                    <div class="bg-yellow-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-yellow-800 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="4" y="4" width="16" height="16" rx="2" ry="2"/>
                                <rect x="9" y="9" width="6" height="6"/>
                                <line x1="9" y1="1" x2="9" y2="4"/>
                                <line x1="15" y1="1" x2="15" y2="4"/>
                                <line x1="9" y1="20" x2="9" y2="23"/>
                                <line x1="15" y1="20" x2="15" y2="23"/>
                                <line x1="20" y1="9" x2="23" y2="9"/>
                                <line x1="20" y1="14" x2="23" y2="14"/>
                                <line x1="1" y1="9" x2="4" y2="9"/>
                                <line x1="1" y1="14" x2="4" y2="14"/>
                            </svg>
                            技术风险
                        </h3>
                        <ul class="space-y-2 text-yellow-700">
                            <li>• 配置冲突：严格的配置验证机制</li>
                            <li>• 计算错误：累计金额计算的准确性保证</li>
                            <li>• 并发问题：分布式锁保证数据一致性</li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"/>
                            </svg>
                            监控告警
                        </h3>
                        <ul class="space-y-2 text-blue-700">
                            <li>• 异常申请监控：单商户短时间大量申请</li>
                            <li>• 免审比例监控：免审比例异常增长</li>
                            <li>• 金额监控：超阈值申请的实时告警</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" class="w-8 h-8 rounded">
                        <span class="text-xl font-bold">微邮付</span>
                    </div>
                    <p class="text-gray-400">
                        账户补贴审核管理系统，智能化审核，提升效率
                    </p>
                </div>
                
                                    <div>
                        <h3 class="text-lg font-semibold mb-4">文档信息</h3>
                        <div class="space-y-2 text-gray-400">
                            <div>版本：v3.0</div>
                            <div>创建时间：2025-05-17</div>
                            <div>更新时间：2024-16-17</div>
                            <div>创建人：潘恒</div>
                            <div>更新内容：新增补贴包免审功能、四类型免审配置、界面优化</div>
                        </div>
                    </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <div class="space-y-2">
                        <a href="#background" class="block text-gray-400 hover:text-white transition-colors">项目背景</a>
                        <a href="#goals" class="block text-gray-400 hover:text-white transition-colors">产品目标</a>
                        <a href="#functions" class="block text-gray-400 hover:text-white transition-colors">功能需求</a>
                        <a href="#workflow" class="block text-gray-400 hover:text-white transition-colors">流程展示</a>
                        <a href="#process" class="block text-gray-400 hover:text-white transition-colors">业务流程</a>
                        <a href="#tech" class="block text-gray-400 hover:text-white transition-colors">技术实现</a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 微邮付. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('shadow-lg');
            } else {
                nav.classList.remove('shadow-lg');
            }
        });
    </script>
</body>
</html> 