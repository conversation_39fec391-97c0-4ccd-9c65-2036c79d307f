<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微邮付 - 审核规则配置</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'Inter', 'Noto Sans SC', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 256px;
            background: var(--bg-container);
            border-right: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            height: 64px;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color-light);
            background: var(--bg-container);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .sidebar-logo img {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 4px;
        }

        .sidebar-nav {
            padding: 16px 8px;
            height: calc(100vh - 96px);
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 0 16px 8px;
            font-size: 12px;
            color: var(--text-disabled);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            margin: 2px 0;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 400;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            cursor: pointer;
            border-radius: 6px;
            position: relative;
        }

        .nav-item:hover {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }

        .nav-item.active {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            font-weight: 500;
        }

        .nav-item .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            opacity: 0.65;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 256px;
            min-height: 100vh;
            background: var(--bg-layout);
        }

        /* 顶部导航 */
        .top-header {
            background: var(--bg-container);
            border-bottom: 1px solid var(--border-color-light);
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-card);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--text-disabled);
        }

        .breadcrumb-active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-blue-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
            font-weight: 500;
            font-size: 14px;
        }

        /* 页面容器 */
        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-select {
            height: 32px;
            padding: 4px 11px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            background: var(--bg-container);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px var(--primary-blue-light);
        }

        .form-input:hover,
        .form-select:hover {
            border-color: var(--primary-blue);
        }

        /* 按钮样式 */
        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-danger {
            background: var(--error-red);
            border-color: var(--error-red);
            color: #ffffff;
        }

        .btn-link {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            padding: 0;
            height: auto;
        }

        .btn-link:hover {
            color: var(--primary-blue-hover);
        }

        /* 表格样式 */
        .table-wrapper {
            overflow: hidden;
            border-radius: 8px 8px 0 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 状态标签 */
        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-enabled {
            background: #f6ffed;
            color: var(--success-green);
        }

        .status-disabled {
            background: #fff2f0;
            color: var(--error-red);
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
        }

        .pagination-info {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .pagination-pages {
            display: flex;
            gap: 4px;
        }

        .page-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            background: var(--bg-container);
            color: var(--text-primary);
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            min-width: 32px;
            text-align: center;
        }

        .page-btn.active {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .page-btn:hover:not(.active) {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }
    </style>
</head>

<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="sidebar-logo">
                <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=32&h=32&fit=crop&crop=center" alt="微邮付" />
                微邮付
            </div>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">工作台</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>
                    首页
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/></svg>
                    交易信息
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">商户管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0a6 6 0 1 0 8 8 6 6 0 0 0-8-8z"/></svg>
                    商户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/></svg>
                    费率修改记录
                </div>
                <div class="nav-item" onclick="window.location.href='账户补贴审核管理系统.html'">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"/><path d="M21 12c.552 0 1-.448 1-1V9a2 2 0 0 0-2-2h-1V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v2H4a2 2 0 0 0-2 2v2c0 .552.448 1 1 1"/></svg>
                    政策审核
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 1v6m0 0 4-4m-4 4L8 3"/><path d="M12 23v-6m0 0 4 4m-4-4-4 4"/></svg>
                    分店管理
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">审核配置</div>
                <div class="nav-item active">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                    审核规则配置
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">其他管理</div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>
                    推导客户管理
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                    商户补贴配置
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <header class="top-header">
            <div class="breadcrumb">
                <svg style="width: 16px; height: 16px; margin-right: 16px; cursor: pointer;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="18" x2="21" y2="18"/>
                </svg>
                <span>首页</span>
                <span class="breadcrumb-separator">/</span>
                <span>审核配置</span>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-active">审核规则配置</span>
            </div>
            <div class="user-info">
                <span style="color: var(--text-secondary);">返回旧版</span>
              
                <span style="color: var(--text-primary);">灵心</span>
                <span style="color: var(--primary-blue); font-weight: 500;">内容超级管理员</span>
            </div>
        </header>

        <!-- 页面内容 -->
        <div class="page-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">审核规则配置</h1>
                <p class="page-description">管理各机构的审核规则，包括金额限制、审批层级等配置</p>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="card">
                <div class="card-body">
                    <div class="form-grid" style="grid-template-columns: repeat(3, 1fr);">
                        <div class="form-group">
                            <label class="form-label">机构名称</label>
                            <select class="form-select">
                                <option>全部机构</option>
                                <option>省分行</option>
                                <option>潮州市分行</option>
                                <option>汕头市分行</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核类型</label>
                            <select class="form-select">
                                <option>全部类型</option>
                                <option>账户补贴</option>
                                <option>费率调整</option>
                                <option>额度审批</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>已启用</option>
                                <option>已停用</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>
                            </svg>
                            搜索
                        </button>
                        <button class="btn btn-default">重置</button>
                    </div>
                </div>
            </div>

            <!-- 数据列表区域 -->
            <div class="card">
                <!-- 列表工具栏 -->
                <div class="card-header">
                    <div class="card-title">规则列表</div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary" onclick="window.location.href='新增审核规则.html'">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M5 12h14"/><path d="M12 5v14"/>
                            </svg>
                            新增规则
                        </button>
                        <button class="btn btn-default">
                            <svg style="width: 14px; height: 14px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 16.5v2a2.5 2.5 0 0 0 2.5 2.5h13a2.5 2.5 0 0 0 2.5-2.5v-2"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                            导出
                        </button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>机构名称</th>
                                <th>审核类型</th>
                                <th>单笔上限</th>
                                <th>月累计上限</th>
                                <th>网点审批</th>
                                <th>区县审批</th>
                                <th>市级审批</th>
                                <th>省级审批</th>
                                <th>省级拒绝权</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="font-weight: 500;">全国分行</td>
                                <td>
                                    <span style="background: var(--primary-blue-light); color: var(--primary-blue); padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">账户补贴</span>
                                </td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">50万元</td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">200万元</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">≤1万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">1-10万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">10-50万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">>50万</td>
                                <td><span style="color: var(--success-green); font-weight: 500;">是</span></td>
                                <td><span class="status-tag status-enabled">已启用</span></td>
                                <td style="color: var(--text-secondary); font-size: 13px;">2024-12-15 10:30</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=1'">编辑</button>
                                        <button class="btn-link" style="color: var(--error-red);">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="font-weight: 500;">潮州市分行</td>
                                <td>
                                    <span style="background: #f0f9ff; color: #0ea5e9; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">费率调整</span>
                                </td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">100万元</td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">500万元</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">≤5万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">5-20万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">20-100万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">>100万</td>
                                <td><span style="color: var(--success-green); font-weight: 500;">是</span></td>
                                <td><span class="status-tag status-enabled">已启用</span></td>
                                <td style="color: var(--text-secondary); font-size: 13px;">2024-12-10 14:20</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=2'">编辑</button>
                                        <button class="btn-link" style="color: var(--error-red);">停用</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="font-weight: 500;">汕头市分行</td>
                                <td>
                                    <span style="background: #fef3c7; color: var(--warning-orange); padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">额度审批</span>
                                </td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">200万元</td>
                                <td style="font-family: 'Courier New', monospace; font-weight: 500;">1000万元</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">≤10万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">10-50万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">50-200万</td>
                                <td style="color: var(--text-secondary); font-size: 13px;">>200万</td>
                                <td><span style="color: var(--error-red); font-weight: 500;">否</span></td>
                                <td><span class="status-tag status-disabled">已停用</span></td>
                                <td style="color: var(--text-secondary); font-size: 13px;">2024-11-28 09:15</td>
                                <td>
                                    <div style="display: flex; gap: 8px;">
                                        <button class="btn-link" onclick="window.location.href='编辑审核规则.html?id=3'">编辑</button>
                                        <button class="btn-link" style="color: var(--success-green);">启用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">共 3 条记录</div>
                    <div class="pagination-controls">
                        <span style="color: var(--text-secondary); font-size: 14px;">10条/页</span>
                        <div class="pagination-pages">
                            <button class="page-btn">‹</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">›</button>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                            前往 
                            <input type="number" value="1" style="width: 60px; padding: 4px 8px; border: 1px solid var(--border-color); border-radius: 4px; text-align: center;" min="1"> 
                            页
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            console.log('审核规则配置页面已加载');
        });
 