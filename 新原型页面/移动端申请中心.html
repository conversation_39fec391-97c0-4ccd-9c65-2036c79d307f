<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请中心 - 微邮付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --postal-green: #00A651;
            --postal-green-light: #E8F5E8;
            --postal-green-dark: #007A3D;
        }
        
        .postal-green {
            background-color: var(--postal-green);
        }
        
        .postal-green-light {
            background-color: var(--postal-green-light);
        }
        
        .text-postal-green {
            color: var(--postal-green);
        }
        
        .border-postal-green {
            border-color: var(--postal-green);
        }
        
        .application-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .application-card:active {
            transform: scale(0.98);
            background-color: #f8f9fa;
        }
        
        .search-input {
            border-radius: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .search-input:focus {
            box-shadow: 0 4px 12px rgba(0, 166, 81, 0.2);
        }
        
        .tab-button {
            transition: all 0.3s ease;
            background: transparent;
            border: none;
            outline: none;
        }

        .tab-button.active {
            color: var(--postal-green);
            border-bottom-color: var(--postal-green) !important;
        }

        .tab-button:not(.active) {
            color: #6b7280;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #059669;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }
        
        .filter-dropdown {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .filter-dropdown.show {
            max-height: 200px;
        }

        .filter-modal {
            animation: modalSlideUp 0.3s ease-out;
        }

        @keyframes modalSlideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .filter-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            background: white;
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            margin: 4px;
        }

        .filter-tag.selected {
            border-color: var(--postal-green);
            background: var(--postal-green-light);
            color: var(--postal-green);
        }

        .filter-tag .check-icon {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 18px;
            height: 18px;
            background: var(--postal-green);
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .filter-tag.selected .check-icon {
            display: flex;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="postal-green text-white px-4 py-3">
        <div class="flex items-center justify-between">
            <button onclick="goBack()" class="p-2 -ml-2">
                <i class="fas fa-chevron-left text-xl"></i>
            </button>
            <h1 class="text-lg font-medium">申请中心</h1>
            <div class="w-10"></div>
        </div>
        
        <!-- 搜索框 -->
        <div class="mt-4 relative">
            <input 
                type="text" 
                placeholder="搜索：商户号/负责人手机号/申请人" 
                class="w-full py-3 px-4 pr-12 search-input text-gray-700 placeholder-gray-500 border-none outline-none"
                id="searchInput"
            >
            <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <i class="fas fa-search text-lg"></i>
            </button>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="mt-3 flex items-center justify-between">
            <button
                onclick="goToCodePlateApply()"
                class="flex items-center space-x-2 bg-white bg-opacity-20 px-3 py-2 rounded-lg"
            >
                <i class="fas fa-plus text-sm"></i>
                <span class="text-sm">码牌申请</span>
            </button>
            <button
                onclick="showFilterModal()"
                class="flex items-center space-x-2 bg-white bg-opacity-20 px-3 py-2 rounded-lg"
            >
                <i class="fas fa-filter text-sm"></i>
                <span class="text-sm">筛选</span>
            </button>
        </div>
    </div>

    <!-- 状态标签页 -->
    <div class="bg-white border-b border-gray-200">
        <div class="flex">
            <button
                class="tab-button active flex-1 py-3 text-sm font-medium border-b-2 border-transparent"
                onclick="switchTab('pending', this)"
            >
                审核中 (3)
            </button>
            <button
                class="tab-button flex-1 py-3 text-sm font-medium border-b-2 border-transparent"
                onclick="switchTab('approved', this)"
            >
                已通过 (4)
            </button>
            <button
                class="tab-button flex-1 py-3 text-sm font-medium border-b-2 border-transparent"
                onclick="switchTab('rejected', this)"
            >
                已驳回 (1)
            </button>
        </div>
    </div>

    <!-- 申请列表 -->
    <div class="px-4 py-4 space-y-3 pb-6" id="applicationList">
        <!-- 申请卡片将通过JavaScript动态生成 -->
    </div>

    <!-- 筛选弹窗 -->
    <div id="filterModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50" onclick="hideFilterModal()"></div>
        <div class="absolute bottom-0 left-0 right-0">
            <div class="filter-modal bg-white rounded-t-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">筛选条件</h3>
                    <button onclick="hideFilterModal()" class="text-gray-400">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">审核类型</h4>
                    <div class="flex flex-wrap">
                        <div class="filter-tag" data-value="码牌申请" onclick="toggleFilterTag(this)">
                            <span>码牌申请</span>
                            <div class="check-icon">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="filter-tag" data-value="进件申请" onclick="toggleFilterTag(this)">
                            <span>进件申请</span>
                            <div class="check-icon">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button
                        onclick="resetFilter()"
                        class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium"
                    >
                        重置
                    </button>
                    <button
                        onclick="applyFilter()"
                        class="flex-1 py-3 px-4 postal-green text-white rounded-lg font-medium"
                    >
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>



    <script>
        // 模拟申请数据
        const applications = [
            {
                id: 1,
                type: '码牌申请',
                applicant: '张三',
                merchantId: '6059254143',
                phone: '15910931129',
                applyTime: '2024-07-20 14:30',
                updateTime: '2024-07-21 09:15',
                status: 'pending'
            },
            {
                id: 2,
                type: '进件申请',
                applicant: '李四',
                
                phone: '13522169621',
                applyTime: '2024-07-19 16:45',
                updateTime: '2024-07-20 11:20',
                status: 'approved'
            },
            {
                id: 3,
                type: '码牌申请',
                applicant: '王五',
                merchantId: '6084068275',
                phone: '18612345678',
                applyTime: '2024-07-18 10:20',
                updateTime: '2024-07-19 15:30',
                status: 'rejected'
            },
            {
                id: 4,
                type: '进件申请',
                applicant: '赵六',
                
                phone: '13911223344',
                applyTime: '2024-07-17 13:15',
                updateTime: '2024-07-18 10:45',
                status: 'approved'
            },
            {
                id: 5,
                type: '码牌申请',
                applicant: '孙七',
                merchantId: '6011135072',
                phone: '15800998877',
                applyTime: '2024-07-16 09:30',
                updateTime: '2024-07-17 14:20',
                status: 'pending'
            },
            {
                id: 6,
                type: '进件申请',
                applicant: '周八',
                
                phone: '13700112233',
                applyTime: '2024-07-15 11:45',
                updateTime: '2024-07-16 16:30',
                status: 'approved'
            },
            {
                id: 7,
                type: '码牌申请',
                applicant: '吴九',
                
                phone: '18511223344',
                applyTime: '2024-07-14 15:20',
                updateTime: '2024-07-15 09:10',
                status: 'pending'
            },
            {
                id: 8,
                type: '进件申请',
                applicant: '郑十',
                
                phone: '13611998877',
                applyTime: '2024-07-13 08:30',
                updateTime: '2024-07-14 12:45',
                status: 'approved'
            },
            {
                id: 9,
                type: '进件申请',
                applicant: '陈小明',
                
                phone: '15912345678',
                applyTime: '2024-07-12 14:15',
                updateTime: '2024-07-13 11:20',
                status: 'rejected'
            }
        ];

        let currentTab = 'pending';
        let filteredApplications = applications;

        // 返回按钮
        function goBack() {
            window.location.href = '移动端首页.html';
        }

        // 跳转到码牌申请页面
        function goToCodePlateApply() {
            window.location.href = '移动端码牌申请.html';
        }



        // 切换标签页
        function switchTab(status, element) {
            // 移除所有标签的active类
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加当前标签的active类
            element.classList.add('active');

            currentTab = status;
            renderApplications();
        }

        // 显示筛选弹窗
        function showFilterModal() {
            document.getElementById('filterModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏筛选弹窗
        function hideFilterModal() {
            document.getElementById('filterModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 切换筛选标签
        function toggleFilterTag(tag) {
            tag.classList.toggle('selected');
        }

        // 重置筛选条件
        function resetFilter() {
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('selected');
            });
        }

        // 应用筛选条件
        function applyFilter() {
            const selectedTags = Array.from(document.querySelectorAll('.filter-tag.selected'))
                .map(tag => tag.dataset.value);

            if (selectedTags.length === 0) {
                filteredApplications = applications;
            } else {
                filteredApplications = applications.filter(app =>
                    selectedTags.includes(app.type)
                );
            }

            renderApplications();
            updateTabCounts();
            hideFilterModal();
        }

        // 获取状态显示文本和样式
        function getStatusInfo(status) {
            switch(status) {
                case 'pending':
                    return { text: '审核中', class: 'status-pending' };
                case 'approved':
                    return { text: '已通过', class: 'status-approved' };
                case 'rejected':
                    return { text: '已驳回', class: 'status-rejected' };
                default:
                    return { text: '未知', class: 'status-pending' };
            }
        }

        // 渲染申请列表
        function renderApplications() {
            const container = document.getElementById('applicationList');
            const filtered = filteredApplications.filter(app => app.status === currentTab);

            if (filtered.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">暂无相关申请</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filtered.map(app => {
                const iconInfo = getTypeIcon(app.type);

                // 根据申请类型决定是否显示商户号
                const merchantIdRow = app.type === '码牌申请' && app.merchantId ? `
                    <div class="flex justify-between">
                        <span>商户号：</span>
                        <span class="text-gray-900">${app.merchantId}</span>
                    </div>
                ` : '';

                return `
                    <div class="application-card bg-white rounded-lg p-4 shadow-sm border border-gray-100"
                         onclick="goToDetail('${app.type}', ${app.id})">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <div class="w-6 h-6 rounded-lg ${iconInfo.bgColor} flex items-center justify-center">
                                        <i class="${iconInfo.icon} text-sm ${iconInfo.textColor}"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-900">${app.type}</h3>
                                </div>
                            </div>
                            <button class="text-gray-400">
                                <i class="fas fa-chevron-right text-lg"></i>
                            </button>
                        </div>

                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>申请人：</span>
                                <span class="text-gray-900">${app.applicant}</span>
                            </div>
                            ${merchantIdRow}
                            <div class="flex justify-between">
                                <span>手机号：</span>
                                <span class="text-gray-900">${app.phone}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>申请时间：</span>
                                <span class="text-gray-900">${app.applyTime}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>更新时间：</span>
                                <span class="text-gray-900">${app.updateTime}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取申请类型对应的图标信息
        function getTypeIcon(type) {
            switch(type) {
                case '码牌申请':
                    return {
                        icon: 'fas fa-credit-card',
                        bgColor: 'bg-blue-100',
                        textColor: 'text-blue-600'
                    };
                case '进件申请':
                    return {
                        icon: 'fas fa-file-alt',
                        bgColor: 'bg-green-100',
                        textColor: 'text-green-600'
                    };
                default:
                    return {
                        icon: 'fas fa-file',
                        bgColor: 'bg-gray-100',
                        textColor: 'text-gray-600'
                    };
            }
        }

        // 跳转到详情页
        function goToDetail(type, id) {
            if (type === '码牌申请') {
                window.location.href = `移动端码牌申请详情.html?id=${id}`;
            } else if (type === '进件申请') {
                window.location.href = `移动端进件申请详情.html?id=${id}`;
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();

            if (searchTerm === '') {
                filteredApplications = applications;
            } else {
                filteredApplications = applications.filter(app =>
                    app.merchantId.toLowerCase().includes(searchTerm) ||
                    app.phone.toLowerCase().includes(searchTerm) ||
                    app.applicant.toLowerCase().includes(searchTerm)
                );
            }

            renderApplications();
            updateTabCounts();
        });

        // 更新标签页计数
        function updateTabCounts() {
            const pendingCount = filteredApplications.filter(app => app.status === 'pending').length;
            const approvedCount = filteredApplications.filter(app => app.status === 'approved').length;
            const rejectedCount = filteredApplications.filter(app => app.status === 'rejected').length;

            document.querySelector('.tab-button:nth-child(1)').innerHTML = `审核中 (${pendingCount})`;
            document.querySelector('.tab-button:nth-child(2)').innerHTML = `已通过 (${approvedCount})`;
            document.querySelector('.tab-button:nth-child(3)').innerHTML = `已驳回 (${rejectedCount})`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderApplications();
            updateTabCounts();
        });

        // 防止弹窗内容区域点击时关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
            const filterModal = document.querySelector('.filter-modal');
            if (filterModal) {
                filterModal.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    </script>
</body>
</html>
